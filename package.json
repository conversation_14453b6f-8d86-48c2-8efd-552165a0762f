{"name": "gin-vue-admin", "version": "2.7.9", "private": true, "scripts": {"serve": "vite --host --mode development", "build": "vite build --mode production", "limit-build": "npm install increase-memory-limit-fixbug cross-env -g && npm run fix-memory-limit && node ./limit && npm run build", "preview": "vite preview", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.10", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vueuse/core": "^11.0.3", "@vueuse/integrations": "^12.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ace-builds": "^1.36.4", "axios": "^1.7.7", "chokidar": "^4.0.0", "dayjs": "^1.11.13", "echarts": "5.5.1", "element-plus": "^2.8.5", "highlight.js": "^11.10.0", "json-bigint": "^1.0.0", "marked": "14.1.1", "marked-highlight": "^2.1.4", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.2.2", "screenfull": "^6.0.2", "sortablejs": "^1.15.3", "tailwindcss": "^3.4.10", "universal-cookie": "^7", "vform3-builds": "^3.0.10", "vue": "^3.5.7", "vue-cropper": "^1.1.4", "vue-echarts": "^7.0.3", "vue-qr": "^4.0.9", "vue-router": "^4.4.3", "vue3-ace-editor": "^2.2.4"}, "devDependencies": {"@eslint/js": "^9.14.0", "@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.21", "dotenv": "^16.4.5", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "sass": "^1.78.0", "terser": "^5.31.6", "vite": "^5.4.14"}}