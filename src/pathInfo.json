{"/src/view/dashboard/components/banner.vue": "Banner", "/src/view/dashboard/components/card.vue": "Card", "/src/view/dashboard/components/charts-content-numbers.vue": "ChartsContentNumbers", "/src/view/dashboard/components/charts-people-numbers.vue": "ChartsPeopleNumbers", "/src/view/dashboard/components/charts.vue": "Charts", "/src/view/dashboard/components/notice.vue": "Notice", "/src/view/dashboard/components/pluginTable.vue": "PluginTable", "/src/view/dashboard/components/quickLinks.vue": "QuickLinks", "/src/view/dashboard/components/table.vue": "Table", "/src/view/dashboard/components/wiki.vue": "Wiki", "/src/view/dashboard/index.vue": "Index", "/src/view/dataCenter/doneOrder.vue": "PaymentOrder", "/src/view/dataCenter/orderReferer.vue": "<PERSON><PERSON><PERSON><PERSON>", "/src/view/dataCenter/paymentRate.vue": "PaymentRate", "/src/view/dataCenter/query.vue": "Query", "/src/view/dataCenter/realtime.vue": "Realtime", "/src/view/dataCenter/transactionOrder.vue": "TransactionOrder", "/src/view/error/index.vue": "Error", "/src/view/error/reload.vue": "Reload", "/src/view/init/index.vue": "Init", "/src/view/layout/aside/asideComponent/asyncSubmenu.vue": "AsyncSubmenu", "/src/view/layout/aside/asideComponent/index.vue": "AsideComponent", "/src/view/layout/aside/asideComponent/menuItem.vue": "MenuItem", "/src/view/layout/aside/combinationMode.vue": "GvaAside", "/src/view/layout/aside/headMode.vue": "GvaAside", "/src/view/layout/aside/index.vue": "Index", "/src/view/layout/aside/normalMode.vue": "GvaAside", "/src/view/layout/header/index.vue": "Index", "/src/view/layout/header/tools.vue": "Tools", "/src/view/layout/iframe.vue": "GvaLayoutIframe", "/src/view/layout/index.vue": "GvaLayout", "/src/view/layout/screenfull/index.vue": "Screenfull", "/src/view/layout/search/search.vue": "BtnBox", "/src/view/layout/setting/index.vue": "GvaSetting", "/src/view/layout/setting/title.vue": "layoutSettingTitle", "/src/view/layout/tabs/index.vue": "HistoryComponent", "/src/view/login/index.vue": "<PERSON><PERSON>", "/src/view/management/adminAdsAccount/adminAdsAccount.vue": "AdminAdsAccount", "/src/view/management/adminFbConvApi/adminFbConvApi.vue": "AdminFbConvApi", "/src/view/management/adminRefererTokens/adminRefererTokens.vue": "AdminRefererTokens", "/src/view/management/dailyStats/dailyStatsCancelCount.vue": "DailyStatsCancelCount", "/src/view/management/dailyStats/dailyStatsFirstAmount.vue": "DailyStatsFirstAmount", "/src/view/management/dailyStats/dailyStatsPaymentDupRate.vue": "DailyStatsPaymentDupRate", "/src/view/management/dailyStats/dailyStatsRefundAmount.vue": "DailyStatsRefundAmount", "/src/view/management/dailyStats/dailyStatsRenewalAmount.vue": "DailyStatsRenewalAmount", "/src/view/management/dailyStats/dailyStatsRenewalRate.vue": "DailyStatsRenewalRate", "/src/view/management/paymentAccounts/paymentAccounts.vue": "PaymentAccounts", "/src/view/management/paymentDispute/paymentDispute.vue": "PaymentDispute", "/src/view/management/paymentOrder/paymentOrder.vue": "PaymentOrder", "/src/view/management/paymentOrder/queryUser.vue": "QueryUser", "/src/view/management/performance/performance.vue": "Performance", "/src/view/management/pixel/pixel.vue": "Pixel", "/src/view/management/subscriptions/subscriptions.vue": "Subscriptions", "/src/view/management/systemConfig/components/selectVideosHidden.vue": "SelectVideosHidden", "/src/view/management/systemConfig/components/selectVideosNormal.vue": "SelectVideosNormal", "/src/view/management/systemConfig/components/selectedVideosHidden.vue": "SelectedVideosHidden", "/src/view/management/systemConfig/systemConfig.vue": "SystemConfig", "/src/view/management/systemConfig/systemConfigForm.vue": "SystemConfigForm", "/src/view/management/tags/tags.vue": "Tags", "/src/view/management/tags/tagsForm.vue": "TagsForm", "/src/view/management/users/users.vue": "Users", "/src/view/management/users/usersForm.vue": "UsersForm", "/src/view/management/videosHidden/videosHidden.vue": "VideosHidden", "/src/view/management/videosNormal/videosNormal.vue": "VideosNormal", "/src/view/management/website/website.vue": "Websites", "/src/view/person/person.vue": "Person", "/src/view/routerHolder.vue": "RouterHolder", "/src/view/superAdmin/api/api.vue": "Api", "/src/view/superAdmin/authority/authority.vue": "Authority", "/src/view/superAdmin/authority/components/apis.vue": "Apis", "/src/view/superAdmin/authority/components/datas.vue": "Datas", "/src/view/superAdmin/authority/components/menus.vue": "Menus", "/src/view/superAdmin/dictionary/sysDictionary.vue": "SysDictionary", "/src/view/superAdmin/dictionary/sysDictionaryDetail.vue": "SysDictionaryDetail", "/src/view/superAdmin/index.vue": "SuperAdmin", "/src/view/superAdmin/menu/components/components-cascader.vue": "ComponentsCascader", "/src/view/superAdmin/menu/icon.vue": "Icon", "/src/view/superAdmin/menu/menu.vue": "Menus", "/src/view/superAdmin/operation/sysOperationRecord.vue": "SysOperationRecord", "/src/view/superAdmin/organization/sysOrganizations.vue": "Organization", "/src/view/superAdmin/organization/sysOrganizationsForm.vue": "OrganizationForm", "/src/view/superAdmin/params/sysParams.vue": "SysParams", "/src/view/superAdmin/user/user.vue": "User", "/src/view/system/state.vue": "State", "/src/view/systemTools/autoCode/component/fieldDialog.vue": "FieldDialog", "/src/view/systemTools/autoCode/component/previewCodeDialog.vue": "PreviewCodeDialog", "/src/view/systemTools/autoCode/index.vue": "AutoCode", "/src/view/systemTools/autoCodeAdmin/index.vue": "AutoCodeAdmin", "/src/view/systemTools/autoPkg/autoPkg.vue": "AutoPkg", "/src/view/systemTools/exportTemplate/exportTemplate.vue": "ExportTemplate", "/src/view/systemTools/formCreate/index.vue": "FormGenerator", "/src/view/systemTools/index.vue": "System", "/src/view/systemTools/installPlugin/index.vue": "Index", "/src/view/systemTools/pubPlug/pubPlug.vue": "PubPlug", "/src/view/systemTools/system/system.vue": "Config", "/src/plugin/announcement/form/info.vue": "InfoForm", "/src/plugin/announcement/view/info.vue": "Info", "/src/plugin/email/view/index.vue": "Email"}