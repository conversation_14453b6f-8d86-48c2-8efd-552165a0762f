<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true">
        <el-form-item label="交易id">
          <el-input-tag
            style="width: 200px;"
            v-model="searchInfo.transaction_ids"
            placeholder="交易id，回车可查询多个"
            aria-label="请按回车可查询多个"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit"
            >查询</el-button
          >
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="multipleTable" style="width: 100%" :data="tableData">
        <el-table-column align="left" label="用户ID" prop="transaction_id" width="160">
          <template #default="scope">{{
            scope.row.attach_info?.user?.id
          }}
          {{ PickAnd<PERSON><PERSON>n<PERSON><PERSON>(scope.row.attach, ' ', "first_name", "last_name", "email") }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="交易ID" prop="transaction_id" width="170">
          <template #default="scope">
              <div v-for="(item,index) in scope.row.attach_info.transactions">
                {{ item.transaction_id }}
              </div>
            </template>
        </el-table-column>
        <el-table-column align="left" label="订阅过期时间" prop="subscriptionExpireAt" width="160">
          <template #default="scope">{{
            formatDate(scope.row.attach_info?.user?.subscriptionExpireAt)
          }}</template>
        </el-table-column>
        
        <el-table-column
          align="left"
          label="订单状态"
          prop="orderStatus"
          width="120"
        >
          <template #default="scope">
            <div v-if="scope.row.paymentOrderId">
              {{ OrderStatusToText(scope.row.orderStatus) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="支付金额"
          prop="firstAmount"
          width="120"
        >
          <template #default="scope">{{
            scope.row.firstAmount / 100
          }}</template>
        </el-table-column>

        <el-table-column align="left" label="网站" prop="website" width="130">
          <template #default="scope">{{
            scope.row.attach_info?.user?.website
          }}</template>
        </el-table-column>
        <el-table-column align="left" label="付款方式" prop="paymentType">
          <template #default="scope">{{
            PaymentTypeToText(scope.row.attach_info?.payment_account?.paymentType)
          }}</template>
        </el-table-column>

        <el-table-column
          align="left"
          label="已解锁剧目"
          fixed="right"
          min-width="240"
        >
          <template #default="scope">
            <el-table
              ref="multipleTable"
              style="width: 100%"
              :data="scope.row.attach_info.videos"
            >
              <el-table-column align="left" label="剧目名称" prop="videoName">
                <template #default="innerScope">
                  <el-tag
                    type="success"
                    v-if="
                      scope.row?.videoId &&
                      innerScope.row.id == scope.row.videoId
                    "
                    >{{ innerScope.row.videoName }}</el-tag
                  >
                  <div v-else>
                    {{ innerScope.row.videoName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="left" label="剧目ID" prop="id" />
            </el-table>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div> -->
    </div>
  </div>
</template>

<script setup>
  import {
    formatDate,
  } from '@/utils/format'
  import { ref, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { GetPaymentOrderWithTransactionIds } from '@/api/realtime/index'
  import { OrderStatusToText, PaymentTypeToText, PickAndJoinJson } from '@/utils/stringFun'

  const searchInfo = ref({
    transaction_ids:[],
  })

  const onSubmit = () => {
    if (!searchInfo.value.transaction_ids) {
      ElMessage.error('请输入交易id')
      return
    }
    getTableData()
  }

  const onReset = () => {
    searchInfo.value = {
      transaction_ids:[]
    }
  }

  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(10)
  const tableData = ref([])
  const getTableData = async () => {
    const table = await GetPaymentOrderWithTransactionIds({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data?.payload
      total.value = table.data?.pagination?.total
      page.value = table.data?.pagination?.page
      pageSize.value = table.data?.pagination?.pageSize
    }
  }
  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }
</script>

<style lang="scss" scoped>
  .padding-l-10 {
    padding-left: 10px;
  }
  .el-row {
    margin-bottom: 24px;
  }
  .gva-search-box {
    margin-bottom: 20px;
  }
</style>
