
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="主键:" prop="id">
          <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入" />
       </el-form-item>
        <el-form-item label="网站:" prop="website">
          <el-input v-model="formData.website" :clearable="true"  placeholder="请输入网站" />
       </el-form-item>
        <el-form-item label="邮箱:" prop="email">
          <el-input v-model="formData.email" :clearable="true"  placeholder="请输入邮箱" />
       </el-form-item>
        <el-form-item label="用户名:" prop="username">
          <el-input v-model="formData.username" :clearable="true"  placeholder="请输入用户名" />
       </el-form-item>
        <el-form-item label="头像:" prop="avatar">
          <el-input v-model="formData.avatar" :clearable="true"  placeholder="请输入头像" />
       </el-form-item>
        <el-form-item label="三方登录 SID:" prop="sid">
          <el-input v-model="formData.sid" :clearable="true"  placeholder="请输入三方登录 SID" />
       </el-form-item>
        <el-form-item label="密码:" prop="password">
          <el-input v-model="formData.password" :clearable="true"  placeholder="请输入密码" />
       </el-form-item>
        <el-form-item label="转化来源:" prop="referer">
          <el-input v-model="formData.referer" :clearable="true"  placeholder="请输入转化来源" />
       </el-form-item>
        <el-form-item label="详细来源:" prop="refererRaw">
          <el-input v-model="formData.refererRaw" :clearable="true"  placeholder="请输入详细来源" />
       </el-form-item>
        <el-form-item label="最后登录 IP:" prop="lastLoginIp">
          <el-input v-model="formData.lastLoginIp" :clearable="true"  placeholder="请输入最后登录 IP" />
       </el-form-item>
        <el-form-item label="最后登录时间:" prop="lastLoginTime">
          <el-date-picker v-model="formData.lastLoginTime" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item label="偏好语言:" prop="language">
          <el-input v-model="formData.language" :clearable="true"  placeholder="请输入偏好语言" />
       </el-form-item>
        <el-form-item label="MP用户:" prop="isAdvance">
          <el-switch v-model="formData.isAdvance" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
       </el-form-item>
        <el-form-item label="E-mail验证标记:" prop="isEmailVerified">
          <el-switch v-model="formData.isEmailVerified" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
       </el-form-item>
        <el-form-item label="匿名用户:" prop="isAnonymous">
          <el-switch v-model="formData.isAnonymous" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
       </el-form-item>
        <el-form-item label="订阅过期时间:" prop="subscriptionExpireAt">
          <el-date-picker v-model="formData.subscriptionExpireAt" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item label="封号:" prop="lockedAt">
          <el-date-picker v-model="formData.lockedAt" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item label="解锁的剧目数:" prop="unlockDrama">
          <el-input v-model.number="formData.unlockDrama" :clearable="true" placeholder="请输入" />
       </el-form-item>
        <el-form-item label="用户类型:" prop="userType">
          <el-input v-model="formData.userType" :clearable="true"  placeholder="请输入用户类型" />
       </el-form-item>
        <el-form-item label="创建时间:" prop="createdAt">
          <el-date-picker v-model="formData.createdAt" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item label="更新时间:" prop="updatedAt">
          <el-date-picker v-model="formData.updatedAt" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createUsers,
  updateUsers,
  findUsers
} from '@/api/management/users'

defineOptions({
    name: 'UsersForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            id: undefined,
            website: '',
            email: '',
            username: '',
            avatar: '',
            sid: '',
            password: '',
            referer: '',
            refererRaw: '',
            lastLoginIp: '',
            lastLoginTime: new Date(),
            language: '',
            isAdvance: false,
            isEmailVerified: false,
            isAnonymous: false,
            subscriptionExpireAt: new Date(),
            lockedAt: new Date(),
            unlockDrama: undefined,
            userType: '',
            createdAt: new Date(),
            updatedAt: new Date(),
        })
// 验证规则
const rule = reactive({
               id : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findUsers({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createUsers(formData.value)
               break
             case 'update':
               res = await updateUsers(formData.value)
               break
             default:
               res = await createUsers(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
