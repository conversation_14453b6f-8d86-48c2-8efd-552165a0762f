<template>
  <div>
    <div class="gva-search-box">
      <el-form
        ref="elSearchFormRef"
        :inline="true"
        :model="searchInfo"
        class="demo-form-inline"
        :rules="searchRule"
        @keyup.enter="onSubmit"
      >
        <el-form-item label="用户ID">
          <el-input
            v-model="searchInfo.id"
            placeholder="用户ID"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="网站">
          <el-input
            v-model="searchInfo.website"
            placeholder="网站"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="转化来源">
          <el-input
            v-model="searchInfo.referer"
            placeholder="转化来源"
            clearable
            style="width: 220px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit"
            >查询</el-button
          >
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column align="left" label="用户ID" prop="id" width="160" />
        <el-table-column align="left" label="网站" prop="website" width="190" />
        <el-table-column align="left" label="邮箱" prop="email" width="160" />
        <el-table-column
          align="left"
          label="转化来源"
          prop="referer"
          width="230"
        />
        <el-table-column
          align="left"
          label="MP"
          prop="isAdvance"
          width="60"
        >
          <template #default="scope">{{
            formatBoolean(scope.row.isAdvance)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="匿名"
          prop="isAnonymous"
          width="60"
        >
          <template #default="scope">{{
            formatBoolean(scope.row.isAnonymous)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="登录IP"
          prop="lastLoginIp"
          width="160"
        />
        <el-table-column
          align="left"
          label="登录时间"
          prop="lastLoginTime"
          width="100"
        >
          <template #default="scope">{{
            formatDate(scope.row.lastLoginTime).substring(0, 10)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="订阅到期"
          prop="subscriptionExpireAt"
          width="100"
        >
          <template #default="scope">{{
            formatDate(scope.row.subscriptionExpireAt).substring(0, 10)
          }}</template>
        </el-table-column>
       
        <el-table-column
          align="right"
          label="操作"
          fixed="right"
          :min-width="160"
        >
          <template #default="scope">
            <el-popconfirm
              class="box-item"
              title="确定重置密码吗?"
              placement="top"
              @confirm="onResetPassword(scope.row)"
            >
              <template #reference>
                <el-button
                  type="primary"
                  link
                  class="table-button"
                ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
                >重置密码</el-button>
              </template>
            </el-popconfirm>

            <el-popconfirm
              class="box-item"
              title="确定刷新登录链接吗?"
              placement="top"
              @confirm="onResetUserToken(scope.row)"
            >
              <template #reference>
                <el-button
                  type="primary"
                  link
                  class="table-button"
                ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
                >登录链接</el-button>
              </template>
            </el-popconfirm>
            
            <el-button
              type="primary"
              link
              class="table-button"
              @click="getDetails(scope.row)"
              ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="edit"
              class="table-button"
              @click="updateUsersFunc(scope.row)"
              >编辑</el-button
            >
              <el-button
                v-if="!scope.row.lockedAt"
                type="warning"
                link
                icon="warning"
                @click="deleteRow(scope.row)"
                >禁用</el-button
              >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="80px"
      >
        <el-form-item label="邮箱:" prop="email">
          <el-input
            v-model="formData.email"
            :clearable="true"
            @blur="handleEmailBlur"
            placeholder="请输入邮箱"
          />
        </el-form-item>
        <el-form-item label="用户名:" prop="username">
          <el-input
            v-model="formData.username"
            :clearable="true"
            placeholder="请输入用户名"
          />
        </el-form-item>
        <!-- <el-form-item label="头像:" prop="avatar">
          <el-input
            v-model="formData.avatar"
            :clearable="true"
            placeholder="请输入头像"
          />
        </el-form-item> -->
       
        <el-form-item label="偏好语言:" prop="language">
          <el-input
            v-model="formData.language"
            :clearable="true"
            placeholder="请输入偏好语言"
          />
        </el-form-item>
        <el-form-item label="MP用户:" prop="isAdvance">
          <el-switch
            v-model="formData.isAdvance"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            clearable
          ></el-switch>
        </el-form-item>
        <el-form-item label="匿名用户:" prop="isAnonymous">
          <el-switch
            v-model="formData.isAnonymous"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            clearable
          ></el-switch>
        </el-form-item>
        <el-form-item label="订阅过期时间:" prop="subscriptionExpireAt">
          <el-date-picker
            v-model="formData.subscriptionExpireAt"
            type="date"
            style="width: 100%"
            placeholder="选择日期"
            :clearable="true"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="detailShow"
      :show-close="true"
      :before-close="closeDetailShow"
      title="查看"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="主键">
          {{ detailFrom.id }}
        </el-descriptions-item>
        <el-descriptions-item label="网站">
          {{ detailFrom.website }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ detailFrom.email }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ detailFrom.username }}
        </el-descriptions-item>
        <el-descriptions-item label="头像">
          {{ detailFrom.avatar }}
        </el-descriptions-item>
        <el-descriptions-item label="三方登录 SID">
          {{ detailFrom.sid }}
        </el-descriptions-item>
        <el-descriptions-item label="密码">
          {{ detailFrom.password }}
        </el-descriptions-item>
        <el-descriptions-item label="转化来源">
          {{ detailFrom.referer }}
        </el-descriptions-item>
        <el-descriptions-item label="详细来源" min-width="120">
          <div style="display: flex;flex-wrap: wrap;word-break: break-all;">
          {{ detailFrom.refererRaw }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="最后登录 IP">
          {{ detailFrom.lastLoginIp }}
        </el-descriptions-item>
        <el-descriptions-item label="最后登录时间">
          {{ detailFrom.lastLoginTime }}
        </el-descriptions-item>
        <el-descriptions-item label="偏好语言">
          {{ detailFrom.language }}
        </el-descriptions-item>
        <el-descriptions-item label="MP用户">
          {{ detailFrom.isAdvance }}
        </el-descriptions-item>
        <el-descriptions-item label="E-mail验证标记">
          {{ detailFrom.isEmailVerified }}
        </el-descriptions-item>
        <el-descriptions-item label="匿名用户">
          {{ detailFrom.isAnonymous }}
        </el-descriptions-item>
        <el-descriptions-item label="订阅过期时间">
          {{ detailFrom.subscriptionExpireAt }}
        </el-descriptions-item>
        <el-descriptions-item label="封号">
          {{ detailFrom.lockedAt }}
        </el-descriptions-item>
        <el-descriptions-item label="解锁的剧目数">
          {{ detailFrom.unlockDrama }}
        </el-descriptions-item>
        <el-descriptions-item label="用户类型">
          {{ detailFrom.userType }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detailFrom.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detailFrom.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup>
  import {
    deleteUsers,
    deleteUsersByIds,
    updateUsers,
    findUsers,
    getUsersList,
    setResetPassword,
    setUserToken,
    setValidateEmail
  } from '@/api/management/users'

  // 全量引入格式化工具 请按需保留
  import {
    getDictFunc,
    formatDate,
    formatBoolean,
    filterDict,
    filterDataSource,
    returnArrImg,
    onDownloadFile
  } from '@/utils/format'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive } from 'vue'
  import { useAppStore } from '@/pinia'

  const onResetPassword = async(row)=>{
    let res = await setResetPassword({
      id: row.id
    })
    if (res.code === 0) {
      ElMessageBox.alert("密码:"+ res.data, '重置成功', {
        confirmButtonText: 'OK',
      })
    }
  }

  const onResetUserToken = async(row)=>{
    let res = await setUserToken({
      user_id: row.id
    })
    if (res.code === 0) {
      ElMessageBox.prompt('登录链接', 'Tip', {
        confirmButtonText: 'Copy',
        inputValue: res.data?.url,
      }).then(({ value }) => {
        navigator.clipboard.writeText(value)
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: 'Copy canceled',
        })
      })
    }
  }


  let isValidateEmail = ref(false)
  const handleEmailBlur = async () => {
    if(formData.value.email){
      let res = await setValidateEmail({
        email: formData.value.email || '',
        website: formData.value.website || '',
      })
      if(res.code == 0){
        if(!formData.value.username && !res.data){
          formData.value.username = formData.value.email;
        }
        isValidateEmail.value = res.data;
      }
      console.log("handleEmailBlur-",res)
    }
  }

  defineOptions({
    name: 'Users'
  })

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  // 控制更多查询条件显示/隐藏状态
  const showAllQuery = ref(false)

  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
    id: undefined,
    email: '',
    username: '',
    avatar: '',
    language: '',
  })

  // 验证规则
  const rule = reactive({
    id: [
      {
        required: true,
        message: '',
        trigger: ['input', 'blur']
      }
    ]
  })

  const searchRule = reactive({
    
  })

  const elFormRef = ref()
  const elSearchFormRef = ref()

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(30)
  const tableData = ref([])
  const searchInfo = ref({})
  // 重置
  const onReset = () => {
    searchInfo.value = {}
    getTableData()
  }

  // 搜索
  const onSubmit = () => {
    elSearchFormRef.value?.validate(async (valid) => {
      if (!valid) return
      page.value = 1
      if (searchInfo.value.isAdvance === '') {
        searchInfo.value.isAdvance = null
      }
      if (searchInfo.value.isEmailVerified === '') {
        searchInfo.value.isEmailVerified = null
      }
      if (searchInfo.value.isAnonymous === '') {
        searchInfo.value.isAnonymous = null
      }
      getTableData()
    })
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查询
  const getTableData = async () => {
    const table = await getUsersList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  getTableData()

  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 多选数据
  const multipleSelection = ref([])
  // 多选
  const handleSelectionChange = (val) => {
    multipleSelection.value = val
  }

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要禁用吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteUsersFunc(row)
    })
  }

  // 多选删除
  const onDelete = async () => {
    ElMessageBox.confirm('确定要禁用吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要禁用的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map((item) => {
          ids.push(item.id)
        })
      const res = await deleteUsersByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '禁用成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
    })
  }

  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 更新行
  const updateUsersFunc = async (row) => {
    const res = await findUsers({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
      formData.value = res.data
      dialogFormVisible.value = true
    }
  }

  // 删除行
  const deleteUsersFunc = async (row) => {
    const res = await deleteUsers({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '禁用成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      website: '',
      email: '',
      username: '',
      avatar: '',
      sid: '',
      password: '',
      referer: '',
      refererRaw: '',
      lastLoginIp: '',
      lastLoginTime: new Date(),
      language: '',
      isAdvance: false,
      isEmailVerified: false,
      isAnonymous: false,
      subscriptionExpireAt: new Date(),
      lockedAt: new Date(),
      unlockDrama: undefined,
      userType: '',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  // 弹窗确定
  const enterDialog = async () => {
    if(isValidateEmail.value){
      return ElMessage({
        type: 'warning',
        message: '邮箱已存在，请重新填写'
      })
    }
    btnLoading.value = true
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createUsers(formData.value)
          break
        case 'update':
          res = await updateUsers(formData.value)
          break
        default:
          res = await createUsers(formData.value)
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '更改成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }

  const detailFrom = ref({})

  // 查看详情控制标记
  const detailShow = ref(false)

  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findUsers({ id: row.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }
</script>

<style></style>
