<template>
  <div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
        </div>
        <el-table
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        >
        
          <el-table-column align="left" label="主键" prop="id" width="160" />
        <el-table-column align="left" label="状态" prop="enabled" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.enabled ? 'success' : 'danger'" size="small">
                {{ scope.row.enabled ? '启用' : '禁用' }}
              </el-tag>
            </template>
        </el-table-column>
          <el-table-column align="left" label="网站" prop="website" width="160" />
          <el-table-column align="left" label="类型" prop="paymentType" width="160" />
          <el-table-column align="left" label="ClientID" prop="clientId" width="130">
            <template #default="scope">
                <span>{{ scope.row.clientId.substring(0, 4) }}****{{ scope.row.clientId.substring(scope.row.clientId.length - 4) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" label="ClientSecret" prop="clientSecret" width="130">
            <template #default="scope">
                <span>{{ scope.row.clientSecret.substring(0, 4) }}****{{ scope.row.clientSecret.substring(scope.row.clientSecret.length - 4) }}</span>
            </template>
          </el-table-column>
        <el-table-column align="left" label="环境" prop="isSandbox" width="120">
            <template #default="scope">
                <el-tag :type="scope.row.isSandbox ? 'danger' : 'success'" size="small">
                  {{ scope.row.isSandbox ? '沙盒' : '正式' }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column align="left" label="代理" prop="proxy" width="320" />
        <el-table-column align="left" label="备注" prop="thirdPartyInfo" width="320" />
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button type="primary" link icon="edit" class="table-button" @click="updatePaymentAccountsFunc(scope.row)">编辑</el-button>
            <el-button type="danger" link icon="delete" class="table-button" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            
            <div v-if="!formData.id">
              <el-form-item label="网站:"  prop="website" >
                <el-input v-model="formData.website" :clearable="true"  placeholder="请输入网站" />
              </el-form-item>
              <el-form-item label="支付类型:"  prop="paymentType" >
                <el-select v-model="formData.paymentType" placeholder="请选择支付类型">
                  <el-option label="PayPal" value="vault_paypal" />
                  <el-option label="信用卡-云汇" value="manual_airwallex" />
                  <el-option label="信用卡-PayPal" value="card_paypal" />
                  <el-option label="信用卡-信用卡1" value="card_easy" />
                  <el-option label="信用卡-信用卡2" value="card_easy_2" />
                  <el-option label="PayPal订阅" value="paypal" disabled />
                  <el-option label="信用卡-云汇订阅" value="airwallex" disabled />
                </el-select>
              </el-form-item>
              <div v-if="formData.website">
                <div v-if="formData.paymentType === 'vault_paypal' || formData.paymentType === 'paypal' || formData.paymentType === 'card_paypal'">
                  <p>回调地址  https://{{  formData.website }}/api/webhook/paypal</p>
                </div>
                <div v-if="formData.paymentType === 'manual_airwallex' || formData.paymentType === 'airwallex'">
                  <p>回调地址  提交后将生成回调地址</p>
                </div>
                <div v-if="formData.paymentType === 'card_easy'">
                  <p>回调地址  https://{{ formData.website }}/api/webhook/card-3</p>
                  <pre>
生成证书:
openssl genpkey -algorithm RSA -pkeyopt rsa_keygen_bits:2048 -out private_key.pem
openssl rsa -in private_key.pem -pubout -out public_key.pem
                  </pre>
                </div>
                <hr style="display: block;margin: 10px;" />
              </div>
              <div v-if="formData.paymentType === 'card_easy' || formData.paymentType === 'card_easy_2'">
                <el-form-item label="MerchantID:"  prop="merchantId" >
                  <el-input v-model="formData.merchantId" :clearable="true"  placeholder="merchantId" />
                </el-form-item>
              </div>
              <el-form-item label="ClientID:"  prop="clientId" >
                <el-input v-model="formData.clientId" :clearable="true"  placeholder="ClientID" />
              </el-form-item>
              <el-form-item label="ClientSecret:"  prop="clientSecret" >
                <el-input v-model="formData.clientSecret" :clearable="true"  placeholder="ClientSecret" />
              </el-form-item>
              <el-form-item label="是否沙盒:"  prop="isSandbox" >
                <el-switch v-model="formData.isSandbox" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
              </el-form-item>
            </div>
            <el-form-item label="Webhook密钥:"  prop="webhookSecret" >
              <el-input v-model="formData.webhookSecret" :clearable="true"  placeholder="请输入Webhook密钥" />
            </el-form-item>
            <div v-if="formData.id">
              <div v-if="formData.paymentType === 'vault_paypal' || formData.paymentType === 'paypal' || formData.paymentType === 'card_paypal'">
                <p>回调地址  https://{{  formData.website }}/api/webhook/paypal</p>
              </div>
              <div v-if="formData.paymentType === 'manual_airwallex' || formData.paymentType === 'airwallex'">
                <p>回调地址  https://{{ formData.website }}/api/webhook/payment/{{ formData.id }}</p>
              </div>
              <div v-if="formData.paymentType === 'card_easy'">
                <p>回调地址  https://{{ formData.website }}/api/webhook/card-3</p>
              </div>
              <hr style="display: block;margin: 10px;" />
            </div>
            <el-form-item label="是否启用:"  prop="enabled" >
              <el-switch v-model="formData.enabled" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
            </el-form-item>
            <el-form-item label="代理:"  prop="proxy" >
              <el-input v-model="formData.proxy" :clearable="true"  placeholder="*******************************.???:8888" />
            </el-form-item>
            <el-form-item label="备注:"  prop="thirdPartyInfo" >
              <el-input v-model="formData.thirdPartyInfo" :clearable="true"  placeholder="" />
            </el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="主键">
                        {{ detailFrom.id }}
                    </el-descriptions-item>
                    <el-descriptions-item label="网站">
                        {{ detailFrom.website }}
                    </el-descriptions-item>
                    <el-descriptions-item label="名称">
                        {{ detailFrom.name }}
                    </el-descriptions-item>
                    <el-descriptions-item label="支付类型">
                        {{ detailFrom.paymentType }}
                    </el-descriptions-item>
                    <el-descriptions-item label="15分钟支付率">
                      {{ detailFrom.paymentRate.fifteenMinutes.checkoutCnt === 0 ? '0' : (
                        (detailFrom.paymentRate.fifteenMinutes.paymentCnt /
                          detailFrom.paymentRate.fifteenMinutes.checkoutCnt) *
                        100
                      ).toFixed(2) }}
                      %
                    </el-descriptions-item>
                    <el-descriptions-item label="1小时支付率">
                      {{ detailFrom.paymentRate.oneHour.checkoutCnt === 0 ? '0' : (
                        (detailFrom.paymentRate.oneHour.paymentCnt /
                          detailFrom.paymentRate.oneHour.checkoutCnt) *
                        100
                      ).toFixed(2) }}
                      %
                    </el-descriptions-item>
                    <el-descriptions-item label="24小时支付率">
                      {{ detailFrom.paymentRate.twentyFourHours.checkoutCnt === 0 ? '0' : (
                        (detailFrom.paymentRate.twentyFourHours.paymentCnt /
                          detailFrom.paymentRate.twentyFourHours.checkoutCnt) *
                        100
                      ).toFixed(2) }}
                      %
                    </el-descriptions-item>
                    <el-descriptions-item label="账号">
                        {{ detailFrom.account }}
                    </el-descriptions-item>
                    <el-descriptions-item label="商户ID">
                        {{ detailFrom.merchantId }}
                    </el-descriptions-item>
                    <el-descriptions-item label="客户端ID">
                        {{ detailFrom.clientId }}
                    </el-descriptions-item>
                    <el-descriptions-item label="客户端密钥">
                        {{ detailFrom.clientSecret }}
                    </el-descriptions-item>
                    <el-descriptions-item label="Webhook密钥">
                        {{ detailFrom.webhookSecret }}
                    </el-descriptions-item>
                    <el-descriptions-item label="是否沙盒">
                        {{ detailFrom.isSandbox }}
                    </el-descriptions-item>
                    <el-descriptions-item label="是否启用">
                        {{ detailFrom.enabled }}
                    </el-descriptions-item>
                    <el-descriptions-item label="支持的卡">
                        {{ detailFrom.supportCards }}
                    </el-descriptions-item>
                    <el-descriptions-item label="备注">
                        {{ detailFrom.thirdPartyInfo }}
                    </el-descriptions-item>
                    <el-descriptions-item label="代理">
                        {{ detailFrom.proxy }}
                    </el-descriptions-item>
                    <el-descriptions-item label="删除时间">
                        {{ detailFrom.deletedAt }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">
                        {{ detailFrom.createdAt }}
                    </el-descriptions-item>
                    <el-descriptions-item label="更新时间">
                        {{ detailFrom.updatedAt }}
                    </el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createPaymentAccounts,
  deletePaymentAccounts,
  deletePaymentAccountsByIds,
  updatePaymentAccounts,
  findPaymentAccounts,
  getPaymentAccountsList
} from '@/api/management/paymentAccounts'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"

defineOptions({
    name: 'PaymentAccounts'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            id: undefined,
            website: '',
            name: '',
            paymentType: '',
            account: '',
            merchantId: '',
            clientId: '',
            clientSecret: '',
            webhookSecret: '',
            isSandbox: false,
            enabled: false,
            supportCards: '',
            thirdPartyInfo: '',
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(30)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    if (searchInfo.value.isSandbox === ""){
        searchInfo.value.isSandbox=null
    }
    if (searchInfo.value.enabled === ""){
        searchInfo.value.enabled=null
    }
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getPaymentAccountsList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要「删除」吗！！！！！！！！！?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deletePaymentAccountsFunc(row)
        })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updatePaymentAccountsFunc = async(row) => {
    const res = await findPaymentAccounts({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deletePaymentAccountsFunc = async (row) => {
    const res = await deletePaymentAccounts({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        id: undefined,
        website: '',
        name: '',
        paymentType: '',
        account: '',
        merchantId: '',
        clientId: '',
        clientSecret: '',
        webhookSecret: '',
        isSandbox: false,
        enabled: false,
        supportCards: '',
        thirdPartyInfo: '',
        proxy: '',
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  if (formData.value.paymentType === 'card_easy') {
                    let clientId = formData.value.clientId.replace(new RegExp(`(.{64})`, 'g'), '$1\n');
                    clientId = "-----BEGIN PUBLIC KEY-----" + "\n" + clientId + "\n" + "-----END PUBLIC KEY-----"
                    formData.value.clientId = clientId
                    let clientSecret = formData.value.clientSecret.replace(new RegExp(`(.{64})`, 'g'), '$1\n');
                    clientSecret = "-----BEGIN PRIVATE KEY-----" + "\n" + clientSecret + "\n" + "-----END PRIVATE KEY-----"
                    formData.value.clientSecret = clientSecret
                  }
                  res = await createPaymentAccounts(formData.value)
                  break
                case 'update':
                  res = await updatePaymentAccounts(formData.value)
                  break
                default:
                  res = await createPaymentAccounts(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}


const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findPaymentAccounts({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>

</style>
