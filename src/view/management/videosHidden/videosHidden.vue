<template>
  <div>
    <div class="gva-search-box">
      <el-form
        ref="elSearchFormRef"
        :inline="true"
        :model="searchInfo"
        class="demo-form-inline"
        :rules="searchRule"
        @keyup.enter="onSubmit"
      >
        <el-form-item label="剧名">
          <el-input
            v-model="searchInfo.videoName"
            placeholder="剧名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit"
            >查询</el-button
          >
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
      >
        <el-table-column align="left" label="主键" prop="id" width="160" />
        <el-table-column
          align="left"
          label="剧名"
          prop="videoName"
          width="250"
        />
        <el-table-column align="left" label="封面" prop="cover" width="120">
          <template #default="scope">
            <img
              :src="getUrl(scope.row.cover)"
              alt=""
              style="width: 100px; height: 100px"
            />
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="播放量"
          prop="playCount"
          width="120"
        />
        <el-table-column
          align="left"
          label="收藏量"
          prop="favorite"
          width="120"
        />
        <el-table-column
          align="left"
          label="描述"
          prop="description"
          width="300"
          show-overflow-tooltip
        />
        <el-table-column
          align="left"
          label="发布时间"
          prop="publishedAt"
          width="180"
        >
          <template #default="scope">{{
            formatDate(scope.row.publishedAt)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="更新时间"
          prop="updatedAt"
          width="180"
        >
          <template #default="scope">{{
            formatDate(scope.row.updatedAt)
          }}</template>
        </el-table-column>
        <el-table-column align="left" label="召回集数" prop="discount_episodes" width="80" />
        <el-table-column
          align="left"
          label="操作"
          fixed="right"
          :min-width="appStore.operateMinWith"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="InfoFilled"
              class="table-button"
              @click="getDetails(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Download"
              class="table-button"
              @click="onDownloadVidoes(scope.row)"
              >下载</el-button
            >
            <el-button
              type="primary"
              link
              icon="edit"
              class="table-button"
              @click="updateVideosHiddenFunc(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              icon="delete"
              @click="deleteRow(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="180px"
      >
        <el-form-item label="剧名:" prop="videoName">
          <el-input
            v-model="formData.videoName"
            :clearable="true"
            placeholder="请输入剧名"
          />
        </el-form-item>
        <el-form-item label="召回集数:" prop="discount_episodes">
          <el-input
            v-model.number="formData.discount_episodes"
            :clearable="true"
            placeholder="0"
          />
        </el-form-item>
        <el-form-item label="封面:" prop="cover">
          <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            list-type="picture"
            accept="image/*"
            :on-success="handleAvatarSuccess"
            :http-request="beforeAvatarUpload"
          >
            <div class="avatar-wrapper" v-if="formData.cover">
              <img :src="getUrl(formData.cover)" class="avatar" />
              <div class="avatar-mask" @click.stop="handleRemove">
                <el-icon><Delete /></el-icon>
              </div>
            </div>
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="播放量:" prop="playCount">
          <el-input
            v-model.number="formData.playCount"
            :clearable="true"
            placeholder="请输入播放量"
          />
        </el-form-item>
        <el-form-item label="收藏量:" prop="favorite">
          <el-input
            v-model.number="formData.favorite"
            :clearable="true"
            placeholder="请输入收藏量"
          />
        </el-form-item>
        <el-form-item label="分享量:" prop="share">
          <el-input
            v-model.number="formData.share"
            :clearable="true"
            placeholder="请输入分享量"
          />
        </el-form-item>
        <el-form-item label="描述:" prop="description">
          <el-input
            v-model="formData.description"
            :clearable="true"
            :autosize="{ minRows: 2}"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="发布时间:" prop="publishedAt">
          <el-date-picker
            v-model="formData.publishedAt"
            type="date"
            style="width: 100%"
            placeholder="选择日期"
            :clearable="true"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="detailShow"
      :show-close="true"
      :before-close="closeDetailShow"
      title="查看"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="主键">
          {{ detailFrom.id }}
        </el-descriptions-item>
        <el-descriptions-item label="剧名">
          {{ detailFrom.videoName }}
        </el-descriptions-item>
        <el-descriptions-item label="召回集数">
          {{ detailFrom.discount_episodes }}
        </el-descriptions-item>
        <el-descriptions-item label="封面">
          <img
            v-if="detailFrom.cover"
            :src="getUrl(detailFrom.cover)"
            style="width: 200px; height: 200px"
          />
        </el-descriptions-item>
        <el-descriptions-item label="播放量">
          {{ detailFrom.playCount }}
        </el-descriptions-item>
        <el-descriptions-item label="收藏量">
          {{ detailFrom.favorite }}
        </el-descriptions-item>
        <el-descriptions-item label="分享量">
          {{ detailFrom.share }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" min-width="80">
          {{ detailFrom.description }}
        </el-descriptions-item>
        <el-descriptions-item label="发布时间">
          {{ detailFrom.publishedAt }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detailFrom.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detailFrom.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>

    <el-dialog
      v-model="downloadParams.show"
      :title="'下载剧集--' + downloadParams.title"
      width="650"
      left
    >
      <el-button
        type="primary"
        class="down-item"
        @click="handleDownAll(downloadParams.id)"
      >
        下载全集1
      </el-button>
      <el-button
        class="down-item"
        @click="generateFFmpegCommands(downloadParams)"
      >
        下载全集2
      </el-button>
      <el-button
          class="down-item"
          v-for="(item, index) in downloadParams.list"
          :key="index"
          @click="handleDownloadVideos(item, index + 1)"
          >{{ index + 1 }}集<el-icon><Download /></el-icon
        ></el-button>
      <div class="flex-wrap" v-if="downloadParams?.captions[0].length>0">
       
        <el-button
          class="down-item"
          v-for="(item, index) in downloadParams.captions"
          :key="index"
          @click="handleDownloadCaptions(item, index + 1)"
          >第{{ index + 1 }}字幕<el-icon><Download /></el-icon
        ></el-button>
      </div>
      <div v-else>
        该剧暂无字幕
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    createVideosHidden,
    deleteVideosHidden,
    deleteVideosHiddenByIds,
    updateVideosHidden,
    findVideosHidden,
    getVideosHiddenList,
    uploadUrlApi
  } from '@/api/management/videosHidden'
  import dayjs from 'dayjs'
  // 全量引入格式化工具 请按需保留
  import {
    CreateUUID,
    formatDate,
    formatBoolean,
    filterDict,
    filterDataSource,
    returnArrImg,
    onDownloadFile
  } from '@/utils/format'
  import { getUrl } from '@/utils/image'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive } from 'vue'
  import { useAppStore } from '@/pinia'
  import { useUserStore } from '@/pinia/modules/user'

  defineOptions({
    name: 'VideosHidden'
  })

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  // 控制更多查询条件显示/隐藏状态
  const showAllQuery = ref(false)

  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
    id: undefined,
    videoName: '',
    cover: '',
    playCount: undefined,
    favorite: undefined,
    share: undefined,
    description: '',
    publishedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  })

  // 验证规则
  const rule = reactive({})

  const searchRule = reactive({})

  const elFormRef = ref()
  const elSearchFormRef = ref()

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(10)
  const tableData = ref([])
  const searchInfo = ref({})
  // 重置
  const onReset = () => {
    searchInfo.value = {}
    getTableData()
  }

  // 搜索
  const onSubmit = () => {
    elSearchFormRef.value?.validate(async (valid) => {
      if (!valid) return
      page.value = 1
      getTableData()
    })
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查询
  const getTableData = async () => {
    const table = await getVideosHiddenList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  getTableData()

  let downloadParams = ref({
    show: false,
    list: [],
    captions: [],
    title: '',
    id: ''
  })
  const onDownloadVidoes = async (row) => {
    const res = await findVideosHidden({ id: row.id })
    if (res.code === 0) {
      downloadParams.value.show = true
      downloadParams.value.title = res.data?.videoName
      downloadParams.value.list = res.data?.episodes
      downloadParams.value.captions = res.data?.captions
      downloadParams.value.id = res.data?.id
    }
  }
  const handleDownloadVideos = (item, index) => {
    if(item.includes("mp4")){
      window.open(item)
    }else{
      window.open(`https://m3u8tomp4.top/?start=true&name=${index}&url=${item}`)
    }
  }
  const handleDownloadCaptions = (item, index)=>{
    window.open(item)
  }
  const handleDownAll = (id) => {
    const userStore = useUserStore()
    const token = userStore.token
    const code = `./rapidxreels.exe download --token=${token} --vid=${id}`
    copyText(code)
  }
  
  function generateFFmpegCommands(data) {
    const { id, list } = data;
    if(list?.length){
      const videoId = id;
      const ffmpegPath = "ffmpeg-7.1.1-essentials_build\\bin\\ffmpeg.exe";
      const mkdirCmd = `mkdir downloads\\${videoId}`;
      const ffmpegCmds = list.map((url, index) => {
        const outputFile = `downloads\\${videoId}\\${index + 1}.mp4`;
        return `${ffmpegPath} -i ${url} ${outputFile}`; // 去掉双引号
      });
      const fullCommand = [mkdirCmd, ...ffmpegCmds].join(" && ");
      copyText(fullCommand)
    }else{
      ElMessage({
        type: 'warning',
        message: '暂无下载链接'
      })
    }
  }


  function copyText(text = "") {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
      const success = document.execCommand("copy");
      if (success) {
      ElMessage({
        type: 'success',
        message: '复制成功'
      })
      } else {
        showToast("Copy fail");
      }
    } catch (err) {
      showToast("Unable to copy text:", err);
    }
    document.body.removeChild(textarea);
  }


  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 多选数据
  const multipleSelection = ref([])
  // 多选
  const handleSelectionChange = (val) => {
    multipleSelection.value = val
  }

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteVideosHiddenFunc(row)
    })
  }

  // 多选删除
  const onDelete = async () => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map((item) => {
          ids.push(item.id)
        })
      const res = await deleteVideosHiddenByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
    })
  }

  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 更新行
  const updateVideosHiddenFunc = async (row) => {
    const res = await findVideosHidden({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
      formData.value = res.data
      dialogFormVisible.value = true
    }
  }

  // 删除行
  const deleteVideosHiddenFunc = async (row) => {
    const res = await deleteVideosHidden({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      videoName: '',
      cover: '',
      playCount: undefined,
      favorite: undefined,
      share: undefined,
      description: '',
      publishedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  // 弹窗确定
  const enterDialog = async () => {
    btnLoading.value = true
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createVideosHidden(formData.value)
          break
        case 'update':
          res = await updateVideosHidden(formData.value)
          break
        default:
          res = await createVideosHidden(formData.value)
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建/更改成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }

  const detailFrom = ref({})

  // 查看详情控制标记
  const detailShow = ref(false)

  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findVideosHidden({ id: row.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }

  import { ElLoading } from 'element-plus'
  // 图片上传
  const presignedS3UploadFile = async (file, presignedUrl) => {
    return new Promise((resolve, reject) => {
      fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      })
      .then(response => {
        if (response.ok) {
          resolve(response?.url?.split('?')[0]);
        } else {
          reject(new Error(`Upload failed: ${response.statusText}`));
        }
        fullscreenLoading.value.close()
      })
      .catch(error => {
        reject(error);
      });
    });
  };

  const handleAvatarSuccess =(event)=>{
    if(event?.url){
      formData.value.cover = event?.url
    }
  }
  const handleRemove = ()=>{
    formData.value.cover = ""
  }
  let fullscreenLoading = ref()
  const beforeAvatarUpload = async (options) => {
    fullscreenLoading.value = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const { file, onSuccess, onError } = options
    try {
      let res = await uploadUrlApi({
        key: `upload/${dayjs().format('YYYYMMDD')}/` + CreateUUID() + '.png'
      })
      if (res.code === 0) {
        const result = await presignedS3UploadFile(file, res.data)
        if (result) {
          onSuccess({ url: result })
        } else {
          onError(new Error('Upload failed'))
        }
      } else {
        onError(new Error(res.message || 'Get presigned URL failed'))
      }
    } catch (err) {
      onError(err)
    }
  }

</script>

<style scoped lang="scss">
.avatar-uploader {
  width: 100px;
  height: 100px;
  position: relative;
}

.avatar-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  object-fit: cover;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.avatar-wrapper:hover .avatar-mask {
  opacity: 1;
}

.avatar-uploader,.avatar-uploader img{
  width: 147px;
  height: 147px;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  border: 1px dashed #ccc;
  font-size: 28px;
  color: #8c939d;
  width: 147px;
  height: 147px;
  text-align: center;
  .down-item {
    margin: 10px 20px 10px 0;
  }
  .flex-wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .el-button {
      width: 100px;
    }
  }
}
</style>
