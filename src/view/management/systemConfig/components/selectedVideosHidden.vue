<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`已选 ${selectedData.title || ''}`"
    width="70%"
    @close="handleClose"
  >
    <el-table
      border
      style="width: 100%; height: 500px"
      :data="selectedData.lists"
      empty-text="暂无数据"
    >
      <el-table-column align="left" label="主键" prop="id" width="160" />
      <el-table-column align="left" label="剧名" prop="videoName" />
      <el-table-column align="left" label="封面" prop="cover" width="120">
        <template #default="scope">
          <img
            :src="getUrl(scope.row.cover)"
            alt=""
            style="width: 100px; height: 100px"
          />
        </template>
      </el-table-column>
      <el-table-column align="left" label="操作" fixed="right" width="80">
        <template #default="{ $index }">
          <el-button
            type="danger"
            link
            icon="Delete"
            @click="handleDelete($index)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref,watch,toRaw} from 'vue'
import { getUrl } from '@/utils/image'
import { ElMessage } from 'element-plus'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({ title: '', lists: [] })
  }
})
const emit = defineEmits(['update:show', 'close', 'select'])

const selectedData = ref({ title: '', lists: [] })

watch(() => props.data, (newVal) => {
  selectedData.value = structuredClone(toRaw(newVal))
}, { deep: true, immediate: true })

const dialogVisible = computed({
  get() {
    return props.show
  },
  set(value) {
    emit('update:show', value)
  }
})

const handleDelete = (index) => {
  if (selectedData.value.lists?.length > index) {
    selectedData.value.lists.splice(index, 1)
  }
}

const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

const handleConfirm = () => {
  emit('select', selectedData.value.lists.map(item => ({
    id: item.id,
    cover: item.cover,
    videoName: item.videoName
  })))
  
  dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.el-image {
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.3s;
  
  &:hover {
    transform: scale(1.05);
  }
}
</style>