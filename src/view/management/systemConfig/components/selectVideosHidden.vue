<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择剧集"
    width="70%"
    @close="cancelSelection"
  >
    <el-form
      :inline="true"
      :model="searchInfo"
      class="demo-form-inline"
      @keyup.enter="onSubmit"
    >
      <el-form-item label="剧名">
        <el-input
          v-model="searchInfo.videoName"
          placeholder="剧名"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="search" @click="onSubmit"
          >查询</el-button
        >
        <el-button icon="refresh" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="chooseTable"
      row-key="id"
      :border="true"
      style="width: 100%; height: 500px"
      @selection-change="handleSelectionChange"
      v-loading="loading"
      :data="tableData"
    >
      <el-table-column type="selection" reserve-selection/>
      <el-table-column align="left" label="主键" prop="id" width="160" />
      <el-table-column align="left" label="剧名" prop="videoName" />
      <el-table-column align="left" label="封面" prop="cover" width="120">
        <template #default="scope">
          <img
            :src="getUrl(scope.row.cover)"
            alt=""
            style="width: 100px; height: 100px"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        label="播放量"
        prop="playCount"
        width="120"
      />
      <el-table-column
        align="left"
        label="收藏量"
        prop="favorite"
        width="120"
      />
      <el-table-column
        align="left"
        label="更新时间"
        prop="updatedAt"
        width="180"
      >
        <template #default="scope">{{
          formatDate(scope.row.updatedAt)
        }}</template>
      </el-table-column>
      <el-table-column
        align="left"
        label="召回集数"
        prop="discount_episodes"
        width="100"
      />
    </el-table>
    <div class="gva-pagination">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <template #footer>
      <el-button @click="cancelSelection">取消</el-button>
      <el-button type="primary" @click="confirmSelection">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    createVideosHidden,
    deleteVideosHidden,
    deleteVideosHiddenByIds,
    updateVideosHidden,
    findVideosHidden,
    getVideosHiddenList,
  } from '@/api/management/videosHidden'
  // 全量引入格式化工具 请按需保留
  import {
    getDictFunc,
    formatDate,
    formatBoolean,
    filterDict,
    filterDataSource,
    returnArrImg,
    onDownloadFile
  } from '@/utils/format'
  import { getUrl } from '@/utils/image'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, computed } from 'vue'
  import { useAppStore } from '@/pinia'
  import { useUserStore } from '@/pinia/modules/user'

  const props = defineProps({
    show: {
      type: Boolean,
      default: false
    }
  })

  const dialogVisible = computed({
    get() {
      return props.show
    },
    set(value) {
      emit('update:show', value)
    }
  })

  const emit = defineEmits(['update:show', 'close', 'select'])

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(10)
  const tableData = ref([])
  const searchInfo = ref({})
  // 重置
  const onReset = () => {
    searchInfo.value = {}
    getTableData()
  }

  // 搜索
  const onSubmit = () => {
    page.value = 1
    getTableData()
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  let loading = ref(false)
  // 查询
  const getTableData = async () => {
    loading.value = true
    const table = await getVideosHiddenList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      loading.value = false
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  getTableData()

  // 多选数据
  const multipleSelection = ref([])

  let chooseTable = ref(null)
  // 多选
  const handleSelectionChange = (val) => {
    multipleSelection.value = chooseTable.value.getSelectionRows()
  }

  const cancelSelection = () => {
    multipleSelection.value = []
    dialogVisible.value = false
    chooseTable.value?.clearSelection()
    emit('close')
  }
  const confirmSelection = () => {
    if (!multipleSelection.value?.length) {
      ElMessage.warning('请至少选择一项')
      return
    }
    let currentSelects = multipleSelection.value.map((item) => {
      return {
        id: item.id,
        cover: item.cover,
        videoName: item.videoName
      }
    })
    emit('select', currentSelects)
    dialogVisible.value = false
    chooseTable.value?.clearSelection()
  }
</script>

<style scoped lang="scss">
  .down-item {
    margin: 10px 20px 10px 0;
  }
  .flex-wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .el-button {
      width: 100px;
    }
  }
</style>
