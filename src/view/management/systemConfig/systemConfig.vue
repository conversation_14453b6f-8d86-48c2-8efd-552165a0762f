<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog()"
          >新增</el-button
        >
      </div>
      <el-table
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
      >
        <el-table-column align="left" label="主键" prop="id" />
        <el-table-column align="left" label="配置键" prop="configKey" />
        <el-table-column
          align="left"
          label="网站名"
          prop="website"
        />
        <el-table-column align="left" label="是否启用" prop="enabled">
          <template #default="scope">{{
            formatBoolean(scope.row.enabled)
          }}</template>
        </el-table-column>
        <el-table-column align="left" label="更新时间" prop="updatedAt">
          <template #default="scope">{{
            formatDate(scope.row.updatedAt)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="操作"
          fixed="right"
          width="300"
          :min-width="appStore.operateMinWith"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              class="table-button"
              @click="getDetails(scope.row)"
              ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="edit"
              class="table-button"
              @click="updateSystemConfigFunc(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              icon="delete"
              @click="deleteRow(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="80px"
      >
        <el-form-item label="配置键:" prop="configKey">
          <el-select
            v-model="formData.configKey"
            @change="handleChangeSelect"
            placeholder="模版类型"
            :disabled="type !== 'create'"
            style="width: 180px"
          >
            <el-option
              v-for="item in configKeyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="网站名:" prop="website">
          <el-input
            v-model="formData.website"
            :clearable="true"
            placeholder="请输入网站名"
          />
        </el-form-item>

        <div v-if="formData.configKey == 'pay_theme'">
          <el-form-item
            label="支付套餐主题:"
            prop="configValue"
          >
            <el-select
              v-model="formData.configValue"
              @change="changePayTheme"
              placeholder="主题模版类型"
              style="width: 180px"
            >
              <el-option
                v-for="item in payThemeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        
        <el-form-item
          v-if="formData.configKey == 'delivery_country'"
          label="配置值:"
          prop="configValue"
        >
          <el-input
            v-model="formData.configValue"
            :clearable="true"
            placeholder="请输入配置值"
          />
        </el-form-item>

        <div v-if="formData.configKey.startsWith('copyright')">
          <el-form-item label="logo（52*52）:" prop="icon">
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              list-type="picture"
              accept="image/*"
              :on-success="handleAvatarSuccess"
              :http-request="beforeAvatarUpload"
            >
              <div class="avatar-wrapper" v-if="formData.configValue.icon">
                <img :src="getUrl(formData.configValue.icon)" class="avatar" />
                <div class="avatar-mask" @click.stop="handleRemove">
                  <el-icon><Delete /></el-icon>
                </div>
              </div>
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item label="域名:">
            <el-input
              v-model="formData.configValue.domain"
              :disabled="type !== 'create'"
              placeholder="请输入域名"
            >
              <template #prepend>copyright_</template>
            </el-input>
          </el-form-item>
          <el-form-item label="底部地址:">
            <el-input
              v-model="formData.configValue.address"
              :clearable="true"
              placeholder="请输入底部地址"
            />
          </el-form-item>
          <el-form-item label="底部公司:">
            <el-input
              v-model="formData.configValue.company"
              :clearable="true"
              placeholder="请输入底部公司"
            />
          </el-form-item>
          <el-form-item label="底部页脚:">
            <el-input
              v-model="formData.configValue.footer"
              :clearable="true"
              placeholder="请输入底部页脚"
            />
          </el-form-item>
          <el-form-item label="facebook:">
            <el-input
              v-model="formData.configValue.facebook"
              :clearable="true"
              placeholder="请输入facebook"
            />
          </el-form-item>
          <el-form-item label="pinterest:">
            <el-input
              v-model="formData.configValue.pinterest"
              :clearable="true"
              placeholder="请输入pinterest"
            />
          </el-form-item>
        </div>

        <div
          class="padding-b-20"
          v-if="
            formData.configKey == 'homepage' ||
            formData.configKey == 'homepage_fake'
          "
        >
          <el-form-item
            v-for="(config, index) in formData.configValue"
            :key="index"
          >
            <div style="display: flex; gap: 10px">
              <el-form-item label="模版类型:" label-position="top">
                <el-select
                  v-model="config.type"
                  placeholder="模版类型"
                  style="width: 140px"
                >
                  <el-option
                    v-for="item in modelTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="标题:" label-position="top">
                <el-input
                  v-model="config.title"
                  placeholder="标题"
                  style="width: 300px"
                />
              </el-form-item>
              <el-form-item label=" " label-position="top">
                <el-button @click="openSelectedVideosHidden(config, index)">
                  已选 {{ config?.lists?.length || 0 }} 部
                </el-button>
                <el-button v-if="formData.configKey == 'homepage'" @click="openSelectVideosHidden(config, index)">
                  选择隐藏剧
                </el-button>
                <el-button v-if="formData.configKey == 'homepage_fake'" @click="openSelectVideosNormal(config, index)">
                  选择安全剧
                </el-button>
              </el-form-item>
              <el-form-item label=" " label-position="top">
                <el-button type="danger" @click="removeConfig(index)">
                  删除
                </el-button>
              </el-form-item>
            </div>
          </el-form-item>
          <el-button type="primary" @click="addConfig"> 添加配置项 </el-button>
        </div>

        <el-form-item label="是否启用:" prop="enabled">
          <el-switch
            v-model="formData.enabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            clearable
          ></el-switch>
        </el-form-item>
      </el-form>
    </el-drawer>
    
    <selectVideosNormal
      :data="formData?.configValue[selectedVideosHiddenIndex]"
      v-if="dialogVisibleNormal"
      :show="dialogVisibleNormal"
      @close="closeSelectedVideosNormalDialog"
      @select="selectSelectedVideosHiddenDialog"
    />

    <selectedVideosHidden
      :data="formData?.configValue[selectedVideosHiddenIndex]"
      v-if="showSelectedVideosHidden"
      :show="showSelectedVideosHidden"
      @close="closeSelectedVideosHiddenDialog"
      @select="selectSelectedVideosHiddenDialog"
    />
    <selectVideosHidden
      :show="dialogVisible"
      @close="closeVideosHiddenDialog"
      @select="selectVideosHiddenDialog"
    />
    
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="detailShow"
      :show-close="true"
      :before-close="closeDetailShow"
      title="查看"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="主键">
          {{ detailFrom.id }}
        </el-descriptions-item>
        <el-descriptions-item label="配置键">
          {{ detailFrom.configKey }}
        </el-descriptions-item>
        <el-descriptions-item label="网站名">
          {{ detailFrom.website }}
        </el-descriptions-item>
        <el-descriptions-item label="配置值">
          {{ detailFrom.configValue }}
        </el-descriptions-item>
        <el-descriptions-item label="是否启用">
          {{ formatBoolean(detailFrom.enabled) }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detailFrom.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detailFrom.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup>
  import {
    createSystemConfig,
    deleteSystemConfig,
    deleteSystemConfigByIds,
    updateSystemConfig,
    findSystemConfig,
    getSystemConfigList
  } from '@/api/management/systemConfig'
  import {
    uploadUrlApi
  } from '@/api/management/videosHidden'
  // 全量引入格式化工具 请按需保留
  import {
    getDictFunc,
    formatDate,
    formatBoolean,
    filterDict,
    filterDataSource,
    returnArrImg,
    CreateUUID
  } from '@/utils/format'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive, computed } from 'vue'
  import { useAppStore } from '@/pinia'
  import { getUrl } from '@/utils/image'
  import dayjs from 'dayjs'
  import selectVideosHidden from './components/selectVideosHidden.vue'
  import selectedVideosHidden from './components/selectedVideosHidden.vue'
  import selectVideosNormal from './components/selectVideosNormal.vue'
  
  defineOptions({
    name: 'SystemConfig'
  })

  const payThemeOptions = ref([
    {
      value: 'item1',
      label: '主题一'
    },
    {
      value: 'item2',
      label: '主题二'
    },
    {
      value: 'item3',
      label: '主题三'
    }
  ])

  const changePayTheme = (event)=>{
    console.log(event)
  }

  const configKeyOptions = ref([
    {
      value: 'delivery_country',
      label: 'delivery_country'
    },
    {
      value: 'pay_theme',
      label: 'pay_theme'
    },
    {
      value: 'homepage',
      label: 'homepage'
    },
    {
      value: 'homepage_fake',
      label: 'homepage_fake'
    },
    {
      value: 'copyright',
      label: 'copyright'
    }
  ])

  const handleChangeSelect = (event)=>{
    if(event == 'delivery_country'){
      formData.value.configValue=""
    } else if (event == 'copyright'){
      formData.value.configValue={}
    }else{
      formData.value.configValue=[]
    }
  }

  const modelTypeOptions = ref([
    {
      value: 'banner',
      label: 'banner'
    },
    {
      value: 'slide',
      label: 'slide'
    },
    {
      value: 'item1',
      label: '主题一'
    },
    {
      value: 'item2',
      label: '主题二'
    },
    {
      value: 'item3',
      label: '主题三'
    }
  ])

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  const formData = ref({
    id: undefined,
    configKey: '',
    configValue: [],
    lists: [],
    website:"",
    enabled: false
  })

  // 验证规则
  const rule = reactive({})

  const elFormRef = ref()

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(30)
  const tableData = ref([])
  const searchInfo = ref({})
  // 查询
  const getTableData = async () => {
    const table = await getSystemConfigList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  getTableData()

  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteSystemConfigFunc(row)
    })
  }
  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 更新行
  const updateSystemConfigFunc = async (row) => {
    const res = await findSystemConfig({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
      if (res.data?.configKey.startsWith('copyright')) {
        Object.assign(formData.value, {
          ...res.data,
          configValue: res?.data?.configValue ? JSON.parse(res.data.configValue) : {},
        })
        Object.assign(formData.value.configValue, {
          domain:formData.value?.configKey?.split("_")[1]||''
        })
      } else if (res.data?.configKey == 'delivery_country') {
        formData.value = res.data
      } else if (res.data?.configKey == 'pay_theme') {
        formData.value = res.data
      } else {
        Object.assign(formData.value, {
          ...res.data,
          configValue: res?.data?.configValue
            ? JSON.parse(res.data.configValue)
            : {}
        })
      }
      dialogFormVisible.value = true
    }
  }

  // 删除行
  const deleteSystemConfigFunc = async (row) => {
    const res = await deleteSystemConfig({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      configKey: '',
      configValue: [],
      lists: [],
      website:"",
      enabled: false
    }
  }
  function convertToArrayOfStrings(input) {
    // 处理数组类型
    if (Array.isArray(input)) {
      return input.map((item) => {
        // 对象类型：尝试提取 id 或其他属性
        if (typeof item === 'object' && item !== null) {
          if (item.id !== undefined) return String(item.id)
          // 可扩展：提取其他属性或 JSON 序列化
          return JSON.stringify(item)
        }
        // 其他类型：直接转为字符串
        return String(item)
      })
    }
    // 处理单个对象
    if (typeof input === 'object' && input !== null) {
      if (input.id !== undefined) return [String(input.id)]
      return [JSON.stringify(input)]
    }
    // 处理原始类型
    return [String(input)]
  }
  // 弹窗确定
  const enterDialog = async () => {
    btnLoading.value = true
    let privatConfigValue = ""
    if(formData.value.configKey == 'homepage' || formData.value.configKey == 'homepage_fake'){
      formData.value.configValue.forEach((item) => {
        if(item.lists){
          item.target = convertToArrayOfStrings(item.lists) || []
        }else{
          item.target = []
        }
      })
      privatConfigValue = JSON.stringify(formData.value.configValue)
    } else if(formData.value.configKey.startsWith('copyright')){
      Object.assign(formData.value.configValue, {
        address: formData.value.configValue.address?.trim() || '',
        company: formData.value.configValue.company?.trim()|| '',
        footer: formData.value.configValue.footer?.trim()|| '',
        facebook: formData.value.configValue.facebook?.trim()|| '',
        pinterest: formData.value.configValue.pinterest?.trim()|| '',
      })
      formData.value.configKey = 'copyright_' + formData.value.configValue.domain?.trim() || ''
      privatConfigValue = JSON.stringify(formData.value.configValue)
    } else {
      privatConfigValue = formData.value.configValue
    }
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createSystemConfig({
            ...formData.value,
            configValue: privatConfigValue
          })
          break
        case 'update':
          res = await updateSystemConfig({
            ...formData.value,
            configValue: privatConfigValue
          })
          break
        default:
          res = await createSystemConfig({
            ...formData.value,
            configValue: privatConfigValue
          })
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建/更改成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }

  const detailFrom = ref({})

  // 查看详情控制标记
  const detailShow = ref(false)

  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findSystemConfig({ id: row.id })
    if (res.code === 0) {
      if (res.data?.configKey == 'delivery_country') {
        detailFrom.value = res.data
      } else if (res.data?.configKey == 'pay_theme') {
        detailFrom.value = res.data
      } else {
        Object.assign(detailFrom.value, {
          ...res.data,
          configValue: res?.data?.configValue
            ? JSON.parse(res.data.configValue)
            : {}
        })
      }
      openDetailShow()
    }
  }

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }

  const addConfig = () => {
    formData.value.configValue.push({
      type: '',
      title: '',
      desc: '',
      target: [],
      lists: [],
      per_page: 6
    })
  }

  const removeConfig = (index) => {
    formData.value?.configValue.splice(index, 1)
  }
  
  const dialogVisible = ref(false)
  let selectedVideosHiddenIndex = ref(0)
  const openSelectVideosHidden = (config, index) => {
    selectedVideosHiddenIndex.value = index
    dialogVisible.value = true
  }

  const closeVideosHiddenDialog = () => {
    dialogVisible.value = false
  }

  const selectVideosHiddenDialog = (row) => {
    // 检查并初始化lists数组
    if (!formData.value.configValue[selectedVideosHiddenIndex.value]) {
      formData.value.configValue[selectedVideosHiddenIndex.value] = {};
    }
    if (!formData.value.configValue[selectedVideosHiddenIndex.value]['lists']) {
      formData.value.configValue[selectedVideosHiddenIndex.value]['lists'] = [];
    }
    // 然后再push
    formData.value.configValue[selectedVideosHiddenIndex.value]['lists'].push(...row);

    closeVideosHiddenDialog()
  }

  let showSelectedVideosHidden = ref(false)
  const openSelectedVideosHidden = (row, index) => {
    selectedVideosHiddenIndex.value = index
    showSelectedVideosHidden.value = true
  }

  const closeSelectedVideosHiddenDialog = () => {
    showSelectedVideosHidden.value = false
  }
  const selectSelectedVideosHiddenDialog = (row) => {
    closeSelectedVideosHiddenDialog()
    closeSelectedVideosNormalDialog()
    // 检查并初始化lists数组
    if (!formData.value.configValue[selectedVideosHiddenIndex.value]) {
      formData.value.configValue[selectedVideosHiddenIndex.value] = {};
    }
    if (!formData.value.configValue[selectedVideosHiddenIndex.value]['lists']) {
      formData.value.configValue[selectedVideosHiddenIndex.value]['lists'] = [];
    }
    // 然后再push
    formData.value.configValue[selectedVideosHiddenIndex.value]['lists'] = row;
  }


  const dialogVisibleNormal = ref(false)
  const openSelectVideosNormal = (config, index)=>{
    dialogVisibleNormal.value = true
    selectedVideosHiddenIndex.value = index
  }
  const closeSelectedVideosNormalDialog = ()=>{
    dialogVisibleNormal.value = false
  }


  import { ElLoading } from 'element-plus'
  // 图片上传
  const presignedS3UploadFile = async (file, presignedUrl) => {
    return new Promise((resolve, reject) => {
      fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      })
      .then(response => {
        if (response.ok) {
          resolve(response?.url?.split('?')[0]);
        } else {
          reject(new Error(`Upload failed: ${response.statusText}`));
        }
        fullscreenLoading.value.close()
      })
      .catch(error => {
        reject(error);
      });
    });
  };

  const handleAvatarSuccess =(event)=>{
    if(event?.url){
      formData.value.configValue.icon = event?.url.replace(/x-short-prod\.s3\.ap-northeast-1\.amazonaws\.com/g, "dk5vk0rwgkm8k.cloudfront.net")
    }
  }
  const handleRemove = ()=>{
    formData.value.configValue.icon = ""
  }
  let fullscreenLoading = ref()
  const beforeAvatarUpload = async (options) => {
    fullscreenLoading.value = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const { file, onSuccess, onError } = options
    try {
      let res = await uploadUrlApi({
        key: `upload/${dayjs().format('YYYYMMDD')}/` + CreateUUID() + '.png'
      })
      if (res.code === 0) {
        const result = await presignedS3UploadFile(file, res.data)
        if (result) {
          onSuccess({ url: result })
        } else {
          onError(new Error('Upload failed'))
        }
      } else {
        onError(new Error(res.message || 'Get presigned URL failed'))
      }
    } catch (err) {
      onError(err)
    }
  }
</script>

<style scoped>
  .avatar-uploader {
    width: 100px;
    height: 100px;
    position: relative;
  }

  .avatar-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: cover;
  }

  .avatar-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
  }

  .avatar-wrapper:hover .avatar-mask {
    opacity: 1;
  }

  .avatar-uploader,.avatar-uploader img{
    width: 147px;
    height: 147px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  .avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
  }

  .el-icon.avatar-uploader-icon {
    border: 1px dashed #ccc;
    font-size: 28px;
    color: #8c939d;
    width: 147px;
    height: 147px;
    text-align: center;
    .down-item {
      margin: 10px 20px 10px 0;
    }
    .flex-wrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .el-button {
        width: 100px;
      }
    }
  }
  .padding-b-20 {
    padding-bottom: 20px;
  }
</style>
