<template>
  <div class="daily-stats-first-amount-container">
    <!-- 查询条件 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" class="query-form">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            @change="handleDateChange"
            style="width: 300px;"
          />
        </el-form-item>
        <el-form-item label="支付账户">
            <el-select v-model="queryParams.accountName" placeholder="请选择支付账户">
            <el-option label="全部" value="all" />
            <el-option
              v-for="item in accountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getFirstAmountData">查询</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 总金额展示 -->
    <el-card class="total-amount-card" shadow="hover">
      <div class="card-content-scrollable">
        <div class="card-content-two-rows">
          <!-- 第一行 -->
          <div class="card-row">
            <div class="stat-item">
              <div class="total-amount-title">总首订金额</div>
              <div class="total-amount-value">${{ formatAmount(totalAmount) }}</div>
            </div>
            <div class="stat-item">
              <div class="total-count-title">总首订数</div>
              <div class="total-count-value">{{ totalCount }}</div>
            </div>
            <div class="stat-item">
              <div class="total-effective-amount-title">总首订有效金额</div>
              <div class="total-effective-amount-value">${{ formatAmount(totalEffectiveAmount) }}</div>
            </div>
            <div class="stat-item">
              <div class="total-effective-count-title">总首订有效数</div>
              <div class="total-effective-count-value">{{ totalEffectiveCount }}</div>
            </div>
            <div class="stat-item">
              <div class="total-rate-title">总重复率</div>
              <div class="total-rate-value">{{ formatRate(totalRate) }}%</div>
            </div>
            <div class="stat-item">
              <div class="total-rate-detail-title">重复数/订单数</div>
              <div class="total-rate-detail-value">{{ totalTargetCnt }} / {{ totalDayCnt }}</div>
            </div>
          </div>
          <!-- 第二行 -->
          <div class="card-row">
            <div class="stat-item">
              <div class="total-cancel-amount-title">总退订金额</div>
              <div class="total-cancel-amount-value">${{ formatAmount(totalCancelAmount) }}</div>
            </div>
            <div class="stat-item">
              <div class="total-cancel-count-title">退订数/订单数</div>
              <div class="total-cancel-count-value">{{ totalCancelCount }} / {{ totalDayCnt }}</div>
            </div>
            <div class="stat-item">
              <div class="total-cancel-rate-title">总退订率</div>
              <div class="total-cancel-rate-value">{{ formatRate(totalCancelRate) }}%</div>
            </div>
            <div class="stat-item">
              <div class="total-refund-amount-title">总退款金额</div>
              <div class="total-refund-amount-value">${{ formatAmount(totalRefundAmount) }}</div>
            </div>
            <div class="stat-item">
              <div class="total-refund-count-title">退款数/订单数</div>
              <div class="total-refund-count-value">{{ totalRefundCount }} / {{ totalDayCnt }}</div>
            </div>
            <div class="stat-item">
              <div class="total-refund-rate-title">总退款率</div>
              <div class="total-refund-rate-value">{{ formatRate(totalRefundRate) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-table-card">
      <div class="table-container">
        <el-table
          :data="mergedTableData"
          border
          stripe
          v-loading="loading"
          style="width: 100%"
        >
          <el-table-column prop="date" align="center" sortable min-width="100">
            <template #header>
              <div class="table-header">
                <span>日期</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="amount" align="center" sortable min-width="130">
            <template #header>
              <div class="table-header">
                <span>首订金额</span>
              </div>
            </template>
            <template #default="scope">
              ${{ formatAmount(scope.row.amount || 0) }}
            </template>
          </el-table-column>
          <el-table-column prop="count" align="center" sortable min-width="80">
            <template #header>
              <div class="table-header">
                <span>首订数</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="effectiveAmount" align="center" sortable min-width="150">
            <template #header>
              <div class="table-header">
                <span>首订有效金额</span>
              </div>
            </template>
            <template #default="scope">
              ${{ formatAmount(scope.row.effectiveAmount || 0) }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveCount" align="center" sortable min-width="130">
            <template #header>
              <div class="table-header">
                <span>首订有效数</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="targetCnt" align="center" sortable min-width="80">
            <template #header>
              <div class="table-header">
                <span>重复数</span>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="dayCnt" label="总订单数" align="center" sortable min-width="100" /> -->
          <el-table-column align="center" sortable min-width="80" :sort-method="sortByDupRate">
            <template #header>
              <div class="table-header">
                <span>重复率</span>
              </div>
            </template>
            <template #default="scope">
              {{ scope.row.dayCnt > 0 ? formatRate((scope.row.targetCnt / scope.row.dayCnt) * 100) : '0.00' }}%
            </template>
          </el-table-column>
          <el-table-column prop="cancelAmount" align="center" sortable min-width="100">
            <template #header>
              <div class="table-header">
                <span>退订金额</span>
              </div>
            </template>
            <template #default="scope">
              ${{ formatAmount(scope.row.cancelAmount || 0) }}
            </template>
          </el-table-column>
          <el-table-column prop="cancelCount" align="center" sortable min-width="80">
            <template #header>
              <div class="table-header">
                <span>退订数</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable min-width="80" :sort-method="sortByCancelRate">
            <template #header>
              <div class="table-header">
                <span>退订率</span>
              </div>
            </template>
            <template #default="scope">
              {{ scope.row.count > 0 ? formatRate((scope.row.cancelCount / scope.row.count) * 100) : '0.00' }}%
            </template>
          </el-table-column>
          <el-table-column prop="refundAmount" align="center" sortable min-width="100">
            <template #header>
              <div class="table-header">
                <span>退款金额</span>
              </div>
            </template>
            <template #default="scope">
              ${{ formatAmount(scope.row.refundAmount || 0) }}
            </template>
          </el-table-column>
          <el-table-column prop="refundCount" align="center" sortable min-width="110">
            <template #header>
              <div class="table-header">
                <span>退款数</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" sortable min-width="80" :sort-method="sortByRefundRate">
            <template #header>
              <div class="table-header">
                <span>退款率</span>
              </div>
            </template>
            <template #default="scope">
              {{ scope.row.count > 0 ? formatRate((scope.row.refundCount / scope.row.count) * 100) : '0.00' }}%
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { getFirstAmountSummary } from '@/api/management/dailyStats'
import { getPaymentAccountNames } from '@/api/management/paymentAccounts'
import { ElMessage } from 'element-plus'

// 转换为-8时区的工具函数
const convertToUTC8 = (date) => {
  const utc = date.getTime() + date.getTimezoneOffset() * 60000
  return new Date(utc - (8 * 60 * 60 * 1000))
}

// 日期范围快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      return [targetToday, targetToday]
    }
  },
  {
    text: '昨天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const yesterday = new Date(targetToday)
      yesterday.setDate(targetToday.getDate() - 1)
      return [yesterday, yesterday]
    }
  },
  {
    text: '最近3天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 2)
      return [start, targetToday]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 6)
      return [start, targetToday]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 29)
      return [start, targetToday]
    }
  },
  {
    text: '当月',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday.getFullYear(), targetToday.getMonth(), 1)
      return [start, targetToday]
    }
  }
]

// 支付账户选项
const accountOptions = ref([])

// 获取支付账户列表
const getAccountOptions = async () => {
  try {
    const { data } = await getPaymentAccountNames()
    if (data && Array.isArray(data)) {
      accountOptions.value = data.map(name => ({
        label: name,
        value: name
      }))
    }
  } catch (error) {
    console.error('获取支付账户列表失败:', error)
    ElMessage.error('获取支付账户列表失败')
  }
}

// 页面加载时获取支付账户列表
onMounted(() => {
  getAccountOptions()
})

// 查询参数
const queryParams = ref({
  bgnDate: '',
  endDate: '',
  accountName: ''
})

// 日期范围
const dateRange = ref([])

// 金额数据
const amountTableData = ref([])
const totalAmount = ref(0)
const totalCount = ref(0)

// 有效订单数据
const effectiveTableData = ref([])
const totalEffectiveAmount = ref(0)
const totalEffectiveCount = ref(0)

// 退订数据
const cancelTableData = ref([])
const totalCancelAmount = ref(0)
const totalCancelCount = ref(0)

// 退款数据
const refundTableData = ref([])
const totalRefundAmount = ref(0)
const totalRefundCount = ref(0)

// 重复率数据
const dupRateTableData = ref([])
const totalTargetCnt = ref(0)
const totalDayCnt = ref(0)
const totalRate = computed(() => {
  return totalDayCnt.value > 0 ? (totalTargetCnt.value / totalDayCnt.value) * 100 : 0
})

// 总退订率
const totalCancelRate = computed(() => {
  return totalCount.value > 0 ? (totalCancelCount.value / totalCount.value) * 100 : 0
})

// 总退款率
const totalRefundRate = computed(() => {
  return totalCount.value > 0 ? (totalRefundCount.value / totalCount.value) * 100 : 0
})

// 合并表格数据
const mergedTableData = computed(() => {
  const dataMap = new Map()

  // 处理金额数据
  amountTableData.value.forEach(item => {
    dataMap.set(item.date, {
      date: item.date,
      amount: item.amount,
      count: item.count,
      effectiveAmount: 0,
      effectiveCount: 0,
      cancelAmount: 0,
      cancelCount: 0,
      refundAmount: 0,
      refundCount: 0,
      targetCnt: 0,
      dayCnt: 0
    })
  })

  // 处理有效订单数据并合并
  effectiveTableData.value.forEach(item => {
    if (dataMap.has(item.date)) {
      const existing = dataMap.get(item.date)
      existing.effectiveAmount = item.amount
      existing.effectiveCount = item.count
    } else {
      dataMap.set(item.date, {
        date: item.date,
        amount: 0,
        count: 0,
        effectiveAmount: item.amount,
        effectiveCount: item.count,
        cancelAmount: 0,
        cancelCount: 0,
        refundAmount: 0,
        refundCount: 0,
        targetCnt: 0,
        dayCnt: 0
      })
    }
  })

  // 处理退款数据并合并
  refundTableData.value.forEach(item => {
    if (dataMap.has(item.date)) {
      const existing = dataMap.get(item.date)
      existing.refundAmount = item.amount
      existing.refundCount = item.count
    } else {
      dataMap.set(item.date, {
        date: item.date,
        amount: 0,
        count: 0,
        effectiveAmount: 0,
        effectiveCount: 0,
        cancelAmount: 0,
        cancelCount: 0,
        refundAmount: item.amount,
        refundCount: item.count,
        targetCnt: 0,
        dayCnt: 0
      })
    }
  })

  // 处理退订数据并合并
  cancelTableData.value.forEach(item => {
    if (dataMap.has(item.date)) {
      const existing = dataMap.get(item.date)
      existing.cancelAmount = item.amount
      existing.cancelCount = item.count
    } else {
      dataMap.set(item.date, {
        date: item.date,
        amount: 0,
        count: 0,
        effectiveAmount: 0,
        effectiveCount: 0,
        cancelAmount: item.amount,
        cancelCount: item.count,
        refundAmount: 0,
        refundCount: 0,
        targetCnt: 0,
        dayCnt: 0
      })
    }
  })

  // 处理重复率数据并合并
  dupRateTableData.value.forEach(item => {
    if (dataMap.has(item.date)) {
      const existing = dataMap.get(item.date)
      existing.targetCnt = item.targetCnt
      existing.dayCnt = item.dayCnt
    } else {
      dataMap.set(item.date, {
        date: item.date,
        amount: 0,
        count: 0,
        effectiveAmount: 0,
        effectiveCount: 0,
        cancelAmount: 0,
        cancelCount: 0,
        refundAmount: 0,
        refundCount: 0,
        targetCnt: item.targetCnt,
        dayCnt: item.dayCnt
      })
    }
  })

  // 转换为数组并按日期排序
  return Array.from(dataMap.values()).sort((a, b) => {
    return new Date(a.date) - new Date(b.date)
  })
})

const loading = ref(false)

// 处理日期变化
const handleDateChange = (val) => {
  if (val) {
    queryParams.value.bgnDate = val[0]
    queryParams.value.endDate = val[1]
  } else {
    queryParams.value.bgnDate = ''
    queryParams.value.endDate = ''
  }
}

// 格式化金额
const formatAmount = (amount) => {
  return parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化比率
const formatRate = (rate) => {
  return parseFloat(rate).toFixed(2)
}

// 重复率排序方法
const sortByDupRate = (a, b) => {
  const rateA = a.dayCnt > 0 ? (a.targetCnt / a.dayCnt) * 100 : 0
  const rateB = b.dayCnt > 0 ? (b.targetCnt / b.dayCnt) * 100 : 0
  return rateA - rateB
}

// 退订率排序方法
const sortByCancelRate = (a, b) => {
  const rateA = a.count > 0 ? (a.cancelCount / a.count) * 100 : 0
  const rateB = b.count > 0 ? (b.cancelCount / b.count) * 100 : 0
  return rateA - rateB
}

// 退款率排序方法
const sortByRefundRate = (a, b) => {
  const rateA = a.count > 0 ? (a.refundCount / a.count) * 100 : 0
  const rateB = b.count > 0 ? (b.refundCount / b.count) * 100 : 0
  return rateA - rateB
}

// 获取首订金额数据
const getFirstAmountData = async () => {
  if (!queryParams.value.bgnDate || !queryParams.value.endDate) {
    ElMessage.warning('请选择日期范围')
    return
  }

  // 创建一个新的查询对象，避免修改原始值
  const queryData = { ...queryParams.value }

  // 如果选择了"全部"，则在发送请求时将accountName设置为空字符串
  if (queryData.accountName === 'all') {
    queryData.accountName = ''
  }

  loading.value = true
  try {
    const { data } = await getFirstAmountSummary(queryData)
    if (data) {
      // 处理首订金额数据
      if (data.firstAmountSummary) {
        amountTableData.value = data.firstAmountSummary.dailyAmountCounts || []
        totalAmount.value = data.firstAmountSummary.totalAmount || 0
        totalCount.value = data.firstAmountSummary.totalCount || 0
      }

      // 处理首订有效订单数据
      if (data.firstAmountEffectiveSummary) {
        effectiveTableData.value = data.firstAmountEffectiveSummary.dailyAmountCounts || []
        totalEffectiveAmount.value = data.firstAmountEffectiveSummary.totalAmount || 0
        totalEffectiveCount.value = data.firstAmountEffectiveSummary.totalCount || 0
      }

      // 处理退订数据
      if (data.cancelSummary) {
        cancelTableData.value = data.cancelSummary.dailyAmountCounts || []
        totalCancelAmount.value = data.cancelSummary.totalAmount || 0
        totalCancelCount.value = data.cancelSummary.totalCount || 0
      }

      // 处理退款数据
      if (data.refundSummary) {
        refundTableData.value = data.refundSummary.dailyAmountCounts || []
        totalRefundAmount.value = data.refundSummary.totalAmount || 0
        totalRefundCount.value = data.refundSummary.totalCount || 0
      }

      // 处理重复率数据
      if (data.paymentDupRateSummary) {
        dupRateTableData.value = data.paymentDupRateSummary.dailyRates || []
        totalTargetCnt.value = data.paymentDupRateSummary.totalTargetCnt || 0
        totalDayCnt.value = data.paymentDupRateSummary.totalDayCnt || 0
      }
    }
  } catch (error) {
    console.error('获取首订金额数据失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 页面加载时设置默认日期为最近7天
onMounted(() => {
  const end = convertToUTC8(new Date())
  const start = new Date(end)
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)

  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  dateRange.value = [formatDate(start), formatDate(end)]
  queryParams.value.bgnDate = dateRange.value[0]
  queryParams.value.endDate = dateRange.value[1]
  queryParams.value.accountName = 'all'
  getFirstAmountData()
})
</script>

<style scoped>
.daily-stats-first-amount-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.total-amount-card {
  margin-bottom: 20px;
  text-align: center;
  padding: 20px;
}

.card-content-scrollable {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}

.card-content-two-rows {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 900px; /* 确保内容有足够宽度 */
  width: max-content; /* 让内容自适应宽度 */
}

.card-row {
  display: flex;
  justify-content: flex-start; /* 移动端左对齐，避免居中导致的截断 */
  align-items: center;
  gap: 20px;
  width: 100%;
}

.stat-item {
  flex: 1;
  padding: 0 10px;
  min-width: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center; /* 统计项内容居中 */
  white-space: nowrap; /* 防止文字换行 */
  flex-shrink: 0; /* 防止项目被压缩 */
}

/* 桌面端居中显示 */
@media (min-width: 769px) {
  .card-content-two-rows {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 900px;
    width: 100%; /* 桌面端使用全宽 */
    max-width: 1200px; /* 设置最大宽度避免过度拉伸 */
    margin: 0 auto; /* 整体内容居中 */
  }

  .card-row {
    justify-content: center; /* 桌面端每行统计项居中显示 */
    max-width: 100%;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .daily-stats-first-amount-container {
    padding: 10px;
  }

  .card-content-scrollable {
    padding-left: 0; /* 确保左侧没有额外的内边距 */
    margin-left: 0;
  }

  .card-content-two-rows {
    min-width: 1200px; /* 移动端需要更大的最小宽度 */
    padding-left: 0; /* 移除左侧内边距 */
  }

  .card-row {
    justify-content: flex-start; /* 强制左对齐 */
    padding-left: 0;
  }

  .stat-item {
    min-width: 180px; /* 移动端增加最小宽度 */
    flex-shrink: 0; /* 防止项目被压缩 */
  }

  .total-amount-card {
    margin-bottom: 15px;
    padding: 15px;
    padding-left: 15px; /* 确保卡片左侧有适当内边距 */
  }

  /* 为移动端添加滚动提示 */
  .card-content-scrollable::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
    pointer-events: none;
  }

  .total-amount-card {
    position: relative;
  }
}

.total-amount-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
  margin-bottom: 0;
}

.total-count-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-count-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-rate-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-rate-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-rate-detail-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-rate-detail-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-cancel-amount-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-cancel-amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-cancel-count-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-cancel-count-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-cancel-rate-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-cancel-rate-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-effective-amount-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-effective-amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-effective-count-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-effective-count-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-refund-amount-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-refund-amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-refund-count-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-refund-count-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-refund-rate-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-refund-rate-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.data-table-card {
  margin-bottom: 20px;
}

.table-container {
  width: 100%;
}

.table-container .el-table {
  width: 100%;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.2;
  white-space: normal;
}

.table-header span {
  display: block;
  margin: 1px 0;
}
</style>
