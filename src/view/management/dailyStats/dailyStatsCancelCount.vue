<template>
  <div class="daily-stats-cancel-count-container">
    <!-- 查询条件 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" class="query-form">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            @change="handleDateChange"
            style="width: 300px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getCancelCountData">查询</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 总退订数和金额展示 -->
    <el-card class="total-count-card" shadow="hover">
      <div class="card-content-flex">
        <div class="stat-item">
          <div class="total-amount-title">总退订金额</div>
          <div class="total-amount-value">${{ formatAmount(totalAmount) }}</div>
        </div>
        <div class="stat-item">
          <div class="total-count-title">总退订数</div>
          <div class="total-count-value">{{ totalCount }}</div>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-table-card">
      <div style="display: flex; justify-content: center;">
        <el-table
          :data="tableData"
          style="width: 50%"
          border
          stripe
          v-loading="loading"
        >
          <el-table-column prop="date" label="日期" align="center" sortable />
          <el-table-column prop="amount" label="退订金额" align="center" sortable>
            <template #default="scope">
              ${{ formatAmount(scope.row.amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="count" label="退订数" align="center" sortable />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getCancelCountSummary } from '@/api/management/dailyStats'
import { ElMessage } from 'element-plus'

// 转换为-8时区的工具函数
const convertToUTC8 = (date) => {
  const utc = date.getTime() + date.getTimezoneOffset() * 60000
  return new Date(utc - (8 * 60 * 60 * 1000))
}

// 查询参数
const queryParams = ref({
  bgnDate: '',
  endDate: ''
})

// 日期范围
const dateRange = ref([])

// 表格数据
const tableData = ref([])
const totalCount = ref(0)
const totalAmount = ref(0)
const loading = ref(false)

// 处理日期变化
const handleDateChange = (val) => {
  if (val) {
    queryParams.value.bgnDate = val[0]
    queryParams.value.endDate = val[1]
  } else {
    queryParams.value.bgnDate = ''
    queryParams.value.endDate = ''
  }
}

// 格式化金额
const formatAmount = (amount) => {
  return parseFloat(amount || 0).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 获取退订数量数据
const getCancelCountData = async () => {
  if (!queryParams.value.bgnDate || !queryParams.value.endDate) {
    ElMessage.warning('请选择日期范围')
    return
  }

  loading.value = true
  try {
    const { data } = await getCancelCountSummary(queryParams.value)
    if (data) {
      tableData.value = data.dailyAmountCounts || []
      totalCount.value = data.totalCount || 0
      totalAmount.value = data.totalAmount || 0
    }
  } catch (error) {
    console.error('获取退订数量数据失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 日期范围快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      return [targetToday, targetToday]
    }
  },
  {
    text: '昨天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const yesterday = new Date(targetToday)
      yesterday.setDate(targetToday.getDate() - 1)
      return [yesterday, yesterday]
    }
  },
  {
    text: '最近3天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 2)
      return [start, targetToday]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 6)
      return [start, targetToday]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 29)
      return [start, targetToday]
    }
  },
  {
    text: '当月',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday.getFullYear(), targetToday.getMonth(), 1)
      return [start, targetToday]
    }
  }
]

// 页面加载时设置默认日期为最近7天
onMounted(() => {
  const end = convertToUTC8(new Date())
  const start = new Date(end)
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
  
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  
  dateRange.value = [formatDate(start), formatDate(end)]
  queryParams.value.bgnDate = dateRange.value[0]
  queryParams.value.endDate = dateRange.value[1]
  getCancelCountData()
})
</script>

<style scoped>
.daily-stats-cancel-count-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.total-count-card {
  margin-bottom: 20px;
  text-align: center;
  padding: 20px;
}

.card-content-flex {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
}

.stat-item {
  flex: 1;
  padding: 0 10px;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.total-count-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-count-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-amount-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.data-table-card {
  margin-bottom: 20px;
}
</style>
