
<template>
  <div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >        
          <el-table-column align="left" label="主键" prop="id" width="160" />
          <el-table-column align="left" label="广告平台" prop="platform" width="120" />
          <el-table-column align="left" label="广告账户ID" prop="accountId" width="200" />
          <el-table-column align="left" label="时区" prop="timezone" width="200" />
         <el-table-column align="left" label="创建时间" prop="createdAt" width="180">
            <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
         </el-table-column>
        <el-table-column align="right" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px" label-min-width="80px">
            <el-form-item label="广告平台:"  prop="platform" >
              <el-select v-model="formData.platform" placeholder="请选择平台">
                <el-option label="facebook" value="facebook" />
              </el-select>
            </el-form-item>
            <el-form-item label="广告账户ID:"  prop="accountId" >
              <el-input v-model="formData.accountId" :clearable="true"  placeholder="请输入广告账户ID" />
            </el-form-item>
            <el-form-item label="时区:"  prop="timezone" >
              <el-select v-model="formData.timezone" placeholder="请选择账户时区">
                <el-option label="UTC-4" value="Etc/GMT+4" />
                <el-option label="UTC-8" value="Etc/GMT+8" />
                <el-option label="UTC+8" value="Etc/GMT-8" />
                <el-option label="UTC" value="Etc/UTC" />
              </el-select>
            </el-form-item>
          </el-form>
    </el-drawer>

  </div>
</template>

<script setup>
import {
  createAdminAdsAccount,
  deleteAdminAdsAccount,
  updateAdminAdsAccount,
  getAdminAdsAccountList
} from '@/api/management/adminAdsAccount'

// 全量引入格式化工具 请按需保留
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"

defineOptions({
    name: 'AdminAdsAccount'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            platform: '',
            accountId: '',
            timezone: '',
        })



// 验证规则
const rule = reactive({
})
const elFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getAdminAdsAccountList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteAdminAdsAccountFunc(row)
        })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 删除行
const deleteAdminAdsAccountFunc = async (row) => {
    const res = await deleteAdminAdsAccount({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        adminId: undefined,
        platform: '',
        accountId: '',
        timezone: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createAdminAdsAccount(formData.value)
                  break
                case 'update':
                  res = await updateAdminAdsAccount(formData.value)
                  break
                default:
                  res = await createAdminAdsAccount(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

</script>

<style>

</style>
