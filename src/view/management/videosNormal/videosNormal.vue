<template>
  <div>
    <div class="gva-search-box">
      <el-form
        ref="elSearchFormRef"
        :inline="true"
        :model="searchInfo"
        class="demo-form-inline"
        :rules="searchRule"
        @keyup.enter="onSubmit"
      >
        <el-form-item label="剧名">
          <el-input
            v-model="searchInfo.videoName"
            placeholder="剧名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit"
            >查询</el-button
          >
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="left" label="主键" prop="id" width="120" />
        <el-table-column
          align="left"
          label="剧名"
          prop="videoName"
          width="200"
        />
        <el-table-column align="left" label="封面" prop="cover" width="120">
          <template #default="scope">
            <img
              style="width: 100px; height: 100px"
              :src="getUrl(scope.row.cover)"
            />
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="播放量"
          prop="playCount"
          width="120"
        />
        <el-table-column
          align="left"
          label="收藏量"
          prop="favorite"
          width="120"
        />
        <!-- <el-table-column align="left" label="分享量" prop="share" width="120" /> -->
        <el-table-column
          align="left"
          label="描述"
          prop="description"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          align="left"
          label="发布时间"
          prop="publishedAt"
          width="180"
        >
          <template #default="scope">{{
            formatDate(scope.row.publishedAt)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="创建时间"
          prop="createdAt"
          width="180"
        >
          <template #default="scope">{{
            formatDate(scope.row.createdAt)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="更新时间"
          prop="updatedAt"
          width="180"
        >
          <template #default="scope">{{
            formatDate(scope.row.updatedAt)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="操作"
          fixed="right"
          :min-width="appStore.operateMinWith"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              class="table-button"
              @click="getDetails(scope.row)"
              ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="edit"
              class="table-button"
              @click="updateVideosNormalFunc(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="80px"
      >
        <el-form-item label="剧名:" prop="videoName">
          <el-input
            v-model="formData.videoName"
            :clearable="true"
            placeholder="请输入剧名"
          />
        </el-form-item>
        <!-- <el-form-item label="封面:" prop="cover">
          <el-input
            v-model="formData.cover"
            :clearable="true"
            placeholder="请输入封面"
          />
        </el-form-item> -->
        <el-form-item label="封面:" prop="cover">
          <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            list-type="picture"
            accept="image/*"
            :on-success="handleAvatarSuccess"
            :http-request="beforeAvatarUpload"
          >
            <div class="avatar-wrapper" v-if="formData.cover">
              <img :src="getUrl(formData.cover)" class="avatar" />
              <div class="avatar-mask" @click.stop="handleRemove">
                <el-icon><Delete /></el-icon>
              </div>
            </div>
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="播放量:" prop="playCount">
          <el-input
            v-model.number="formData.playCount"
            :clearable="true"
            placeholder="请输入播放量"
          />
        </el-form-item>
        <el-form-item label="收藏量:" prop="favorite">
          <el-input
            v-model.number="formData.favorite"
            :clearable="true"
            placeholder="请输入收藏量"
          />
        </el-form-item>
        <el-form-item label="分享量:" prop="share">
          <el-input
            v-model.number="formData.share"
            :clearable="true"
            placeholder="请输入分享量"
          />
        </el-form-item>
        <el-form-item label="描述:" prop="description">
          <el-input
            v-model="formData.description"
            :clearable="true"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="detailShow"
      :show-close="true"
      :before-close="closeDetailShow"
      title="查看"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="主键">
          {{ detailFrom.id }}
        </el-descriptions-item>
        <el-descriptions-item label="剧名">
          {{ detailFrom.videoName }}
        </el-descriptions-item>
        <el-descriptions-item label="封面">
          <img
            v-if="detailFrom.cover"
            :src="getUrl(detailFrom.cover)"
            style="width: 200px; height: 200px"
          />
        </el-descriptions-item>
        <el-descriptions-item label="播放量">
          {{ detailFrom.playCount }}
        </el-descriptions-item>
        <el-descriptions-item label="收藏量">
          {{ detailFrom.favorite }}
        </el-descriptions-item>
        <el-descriptions-item label="分享量">
          {{ detailFrom.share }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" min-width="80">
          {{ detailFrom.description }}
        </el-descriptions-item>
        <el-descriptions-item label="发布时间">
          {{ detailFrom.publishedAt }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detailFrom.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detailFrom.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup>
  import {
    createVideosNormal,
    deleteVideosNormal,
    deleteVideosNormalByIds,
    updateVideosNormal,
    findVideosNormal,
    getVideosNormalList
  } from '@/api/management/videosNormal'
  import {
    uploadUrlApi
  } from '@/api/management/videosHidden'
  import dayjs from 'dayjs'
  // 全量引入格式化工具 请按需保留
  import {
    getDictFunc,
    formatDate,
    formatBoolean,
    filterDict,
    filterDataSource,
    returnArrImg,
    onDownloadFile
  } from '@/utils/format'
  import { getUrl } from '@/utils/image'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive } from 'vue'
  import { useAppStore } from '@/pinia'

  defineOptions({
    name: 'VideosNormal'
  })

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  // 控制更多查询条件显示/隐藏状态
  const showAllQuery = ref(false)

  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
    id: undefined,
    videoName: '',
    cover: '',
    playCount: undefined,
    favorite: undefined,
    share: undefined,
    description: '',
    publishedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  })

  // 验证规则
  const rule = reactive({})

  const searchRule = reactive({
   
  })

  const elFormRef = ref()
  const elSearchFormRef = ref()

  const filterCover = (url) => {
    return 'https://d1t6z7n6cempmp.cloudfront.net' + url
  }

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(10)
  const tableData = ref([])
  const searchInfo = ref({})
  // 重置
  const onReset = () => {
    searchInfo.value = {}
    getTableData()
  }

  // 搜索
  const onSubmit = () => {
    elSearchFormRef.value?.validate(async (valid) => {
      if (!valid) return
      page.value = 1
      getTableData()
    })
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查询
  const getTableData = async () => {
    const table = await getVideosNormalList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  getTableData()

  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 多选数据
  const multipleSelection = ref([])
  // 多选
  const handleSelectionChange = (val) => {
    multipleSelection.value = val
  }

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteVideosNormalFunc(row)
    })
  }

  // 多选删除
  const onDelete = async () => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map((item) => {
          ids.push(item.id)
        })
      const res = await deleteVideosNormalByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
    })
  }

  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 更新行
  const updateVideosNormalFunc = async (row) => {
    const res = await findVideosNormal({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
      formData.value = res.data
      dialogFormVisible.value = true
    }
  }

  // 删除行
  const deleteVideosNormalFunc = async (row) => {
    const res = await deleteVideosNormal({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      videoName: '',
      cover: '',
      playCount: undefined,
      favorite: undefined,
      share: undefined,
      description: '',
      publishedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  // 弹窗确定
  const enterDialog = async () => {
    btnLoading.value = true
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createVideosNormal(formData.value)
          break
        case 'update':
          res = await updateVideosNormal(formData.value)
          break
        default:
          res = await createVideosNormal(formData.value)
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建/更改成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }

  const detailFrom = ref({})

  // 查看详情控制标记
  const detailShow = ref(false)

  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findVideosNormal({ id: row.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }

  // 图片上传
  const presignedS3UploadFile = async (file, presignedUrl) => {
    return new Promise((resolve, reject) => {
      fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      })
      .then(response => {
        if (response.ok) {
          resolve(response?.url?.split('?')[0]);
        } else {
          reject(new Error(`Upload failed: ${response.statusText}`));
        }
        fullscreenLoading.value.close()
      })
      .catch(error => {
        reject(error);
      });
    });
  };

  const handleAvatarSuccess =(event)=>{
    if(event?.url){
      formData.value.cover = event?.url
    }
  }
  const handleRemove = ()=>{
    formData.value.cover = ""
  }

  let fullscreenLoading = ref()

  const beforeAvatarUpload = async (options) => {
    const { file, onSuccess, onError } = options
    fullscreenLoading.value = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      let res = await uploadUrlApi({
        key: `upload/${dayjs().format('YYYYMMDD')}/` + CreateUUID() + '.png'
      })
      if (res.code === 0) {
        const result = await presignedS3UploadFile(file, res.data)
        if (result) {
          onSuccess({ url: result })
        } else {
          onError(new Error('Upload failed'))
        }
      } else {
        onError(new Error(res.message || 'Get presigned URL failed'))
      }
    } catch (err) {
      onError(err)
    }
  }
</script>

<style scoped lang="scss">
.avatar-uploader {
  width: 100px;
  height: 100px;
  position: relative;
}

.avatar-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  object-fit: cover;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.avatar-wrapper:hover .avatar-mask {
  opacity: 1;
}

.avatar-uploader,.avatar-uploader img{
  width: 147px;
  height: 147px;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  border: 1px dashed #ccc;
  font-size: 28px;
  color: #8c939d;
  width: 147px;
  height: 147px;
  text-align: center;
  .down-item {
    margin: 10px 20px 10px 0;
  }
  .flex-wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .el-button {
      width: 100px;
    }
  }
}
</style>