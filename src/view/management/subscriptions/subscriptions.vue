<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog()"
          >新增</el-button
        >
      </div>
      <el-table
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
      >

        <el-table-column align="left" label="主键" prop="id" width="160" />
        <el-table-column align="left" label="标题" prop="title" width="220" />
        <el-table-column
          align="left"
          label="副标题"
          prop="subTitle"
          width="220"
        />
        <el-table-column align="left" label="标语" prop="slogon" width="180" />
        <el-table-column align="left" label="描述" prop="desc" width="270" />
        <el-table-column
          align="left"
          label="排序"
          prop="level"
          width="60"
        />
        <el-table-column align="left" label="天数" prop="days" width="60" />
        <el-table-column align="left" label="续费价格" prop="price" width="80">
          <template #default="scope">{{
            scope.row.price / 100
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="首订价格"
          prop="firstPrice"
          width="80"
        >
          <template #default="scope">{{
            scope.row.firstPrice / 100
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="是否启用"
          prop="enabled"
          width="80"
        >
          <template #default="scope">{{
            formatBoolean(scope.row.enabled)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="是否默认"
          prop="isDefault"
          width="120"
        >
          <template #default="scope">{{
            formatBoolean(scope.row.isDefault)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="解锁剧目数"
          prop="unlockDrama"
          width="120"
        />
        <el-table-column
          align="left"
          label="操作"
          fixed="right"
          :min-width="appStore.operateMinWith"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              class="table-button"
              @click="getDetails(scope.row)"
              ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="edit"
              class="table-button"
              @click="updateSubscriptionsFunc(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              icon="delete"
              @click="deleteRow(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="80px"
      >
        <el-form-item label="标题:" prop="title">
          <el-input
            v-model="formData.title"
            :clearable="true"
            placeholder="请输入标题"
          />
        </el-form-item>
        <el-form-item label="副标题:" prop="subTitle">
          <el-input
            v-model="formData.subTitle"
            :clearable="true"
            placeholder="请输入副标题"
          />
        </el-form-item>
        <el-form-item label="标语:" prop="slogon">
          <el-input
            v-model="formData.slogon"
            :clearable="true"
            placeholder="请输入标语"
          />
        </el-form-item>
        <el-form-item label="描述:" prop="desc">
          <el-input
            v-model="formData.desc"
            :clearable="true"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="会员等级:" prop="level">
          <el-input
            v-model.number="formData.level"
            :clearable="true"
            placeholder="请输入会员等级"
          />
        </el-form-item>
        <div v-if="!formData?.id">
          <el-form-item label="价格:" prop="price">
            <el-input
              v-model.number="formData.price"
              :clearable="true"
              placeholder="请输入价格"
            />
          </el-form-item>
          <el-form-item label="首月优惠价格:" prop="firstPrice">
            <el-input
              v-model.number="formData.firstPrice"
              :clearable="true"
              placeholder="请输入首月优惠价格"
            />
          </el-form-item>
          <el-form-item label="解锁的剧目数:" prop="unlockDrama">
          <el-input
            v-model.number="formData.unlockDrama"
            :clearable="true"
            placeholder="请输入解锁的剧目数"
          />
          </el-form-item>
          <el-form-item label="套餐类型:" prop="planType">
            <el-input
              v-model="formData.planType"
              :clearable="true"
              placeholder="请输入套餐类型"
            />
          </el-form-item>
          <el-form-item label="天数:" prop="days">
            <el-input
              v-model.number="formData.days"
              :clearable="true"
              placeholder="请输入天数"
            />
          </el-form-item>
        </div>
        <el-form-item label="是否启用:" prop="enabled">
          <el-switch
            v-model="formData.enabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            clearable
          ></el-switch>
        </el-form-item>
        <el-form-item label="是否默认:" prop="isDefault">
          <el-switch
            v-model="formData.isDefault"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            clearable
          ></el-switch>
        </el-form-item>
        
      </el-form>
    </el-drawer>

    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="detailShow"
      :show-close="true"
      :before-close="closeDetailShow"
      title="查看"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="主键">
          {{ detailFrom.id }}
        </el-descriptions-item>
        <el-descriptions-item label="标题">
          {{ detailFrom.title }}
        </el-descriptions-item>
        <el-descriptions-item label="副标题">
          {{ detailFrom.subTitle }}
        </el-descriptions-item>
        <el-descriptions-item label="标语">
          {{ detailFrom.slogon }}
        </el-descriptions-item>
        <el-descriptions-item label="描述">
          {{ detailFrom.desc }}
        </el-descriptions-item>
        <el-descriptions-item label="会员等级">
          {{ detailFrom.level }}
        </el-descriptions-item>
        <el-descriptions-item label="天数">
          {{ detailFrom.days }}
        </el-descriptions-item>
        <el-descriptions-item label="价格">
          {{ detailFrom.price }}
        </el-descriptions-item>
        <el-descriptions-item label="首月优惠价格">
          {{ detailFrom.firstPrice }}
        </el-descriptions-item>
        <el-descriptions-item label="是否启用">
          {{ formatBoolean(detailFrom.enabled) }}
        </el-descriptions-item>
        <el-descriptions-item label="是否默认">
          {{ formatBoolean(detailFrom.isDefault) }}
        </el-descriptions-item>
        <el-descriptions-item label="解锁的剧目数">
          {{ detailFrom.unlockDrama }}
        </el-descriptions-item>
        <el-descriptions-item label="套餐类型">
          {{ detailFrom.planType }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detailFrom.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detailFrom.updatedAt }}
        </el-descriptions-item>
        <el-descriptions-item label="删除时间">
          {{ detailFrom.deletedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup>
  import {
    createSubscriptions,
    deleteSubscriptions,
    deleteSubscriptionsByIds,
    updateSubscriptions,
    findSubscriptions,
    getSubscriptionsList
  } from '@/api/management/subscriptions'

  // 全量引入格式化工具 请按需保留
  import {
    getDictFunc,
    formatDate,
    formatBoolean,
    filterDict,
    filterDataSource,
    returnArrImg,
    onDownloadFile
  } from '@/utils/format'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive } from 'vue'
  import { useAppStore } from '@/pinia'

  defineOptions({
    name: 'Subscriptions'
  })

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  // 控制更多查询条件显示/隐藏状态
  const showAllQuery = ref(false)

  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
    id: undefined,
    title: '',
    subTitle: '',
    slogon: '',
    desc: '',
    level: undefined,
    days: undefined,
    price: undefined,
    firstPrice: undefined,
    enabled: false,
    isDefault: false,
    unlockDrama: undefined,
    planType: ''
  })

  // 验证规则
  const rule = reactive({})

  const searchRule = reactive({
    // createdAt: [
    //   {
    //     validator: (rule, value, callback) => {
    //       if (
    //         searchInfo.value.startCreatedAt &&
    //         !searchInfo.value.endCreatedAt
    //       ) {
    //         callback(new Error('请填写结束日期'))
    //       } else if (
    //         !searchInfo.value.startCreatedAt &&
    //         searchInfo.value.endCreatedAt
    //       ) {
    //         callback(new Error('请填写开始日期'))
    //       } else if (
    //         searchInfo.value.startCreatedAt &&
    //         searchInfo.value.endCreatedAt &&
    //         (searchInfo.value.startCreatedAt.getTime() ===
    //           searchInfo.value.endCreatedAt.getTime() ||
    //           searchInfo.value.startCreatedAt.getTime() >
    //             searchInfo.value.endCreatedAt.getTime())
    //       ) {
    //         callback(new Error('开始日期应当早于结束日期'))
    //       } else {
    //         callback()
    //       }
    //     },
    //     trigger: 'change'
    //   }
    // ]
  })

  const elFormRef = ref()
  const elSearchFormRef = ref()

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(30)
  const tableData = ref([])
  const searchInfo = ref({})
  // 重置
  const onReset = () => {
    searchInfo.value = {}
    getTableData()
  }

  // 搜索
  const onSubmit = () => {
    elSearchFormRef.value?.validate(async (valid) => {
      if (!valid) return
      page.value = 1
      if (searchInfo.value.enabled === '') {
        searchInfo.value.enabled = null
      }
      if (searchInfo.value.isDefault === '') {
        searchInfo.value.isDefault = null
      }
      getTableData()
    })
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查询
  const getTableData = async () => {
    const table = await getSubscriptionsList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  getTableData()

  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 多选数据
  const multipleSelection = ref([])
  // 多选
  const handleSelectionChange = (val) => {
    multipleSelection.value = val
  }

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteSubscriptionsFunc(row)
    })
  }

  // 多选删除
  const onDelete = async () => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map((item) => {
          ids.push(item.id)
        })
      const res = await deleteSubscriptionsByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
    })
  }

  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 更新行
  const updateSubscriptionsFunc = async (row) => {
    const res = await findSubscriptions({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
      formData.value = res.data
      dialogFormVisible.value = true
    }
  }

  // 删除行
  const deleteSubscriptionsFunc = async (row) => {
    const res = await deleteSubscriptions({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      title: '',
      subTitle: '',
      slogon: '',
      desc: '',
      level: undefined,
      days: undefined,
      price: undefined,
      firstPrice: undefined,
      enabled: false,
      isDefault: false,
      deletedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      unlockDrama: undefined,
      planType: ''
    }
  }
  // 弹窗确定
  const enterDialog = async () => {
    btnLoading.value = true
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createSubscriptions(formData.value)
          break
        case 'update':
          res = await updateSubscriptions(formData.value)
          break
        default:
          res = await createSubscriptions(formData.value)
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建/更改成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }

  const detailFrom = ref({})

  // 查看详情控制标记
  const detailShow = ref(false)

  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findSubscriptions({ id: row.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }
</script>

<style></style>
