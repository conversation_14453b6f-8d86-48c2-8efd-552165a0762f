<template>
  <el-drawer
    destroy-on-close
    :size="appStore.drawerSize"
    v-model="detailShow"
    :show-close="true"
    :before-close="closeDetailShow"
    title="查看"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item label="主键">
        {{ detailFrom.id }}
      </el-descriptions-item>
      <el-descriptions-item label="网站">
        {{ detailFrom.website }}
      </el-descriptions-item>
      <el-descriptions-item label="邮箱">
        {{ detailFrom.email }}
      </el-descriptions-item>
      <el-descriptions-item label="用户名">
        {{ detailFrom.username }}
      </el-descriptions-item>
      <el-descriptions-item label="头像">
        {{ detailFrom.avatar }}
      </el-descriptions-item>
      <el-descriptions-item label="三方登录 SID">
        {{ detailFrom.sid }}
      </el-descriptions-item>
      <el-descriptions-item label="密码">
        {{ detailFrom.password }}
      </el-descriptions-item>
      <el-descriptions-item label="转化来源">
        {{ detailFrom.referer }}
      </el-descriptions-item>
      <el-descriptions-item label="详细来源" min-width="120">
        <div style="display: flex;flex-wrap: wrap;word-break: break-all;">
          {{ detailFrom.refererRaw }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="最后登录 IP">
        {{ detailFrom.lastLoginIp }}
      </el-descriptions-item>
      <el-descriptions-item label="最后登录时间">
        {{ detailFrom.lastLoginTime }}
      </el-descriptions-item>
      <el-descriptions-item label="偏好语言">
        {{ detailFrom.language }}
      </el-descriptions-item>
      <el-descriptions-item label="MP用户">
        {{ detailFrom.isAdvance }}
      </el-descriptions-item>
      <el-descriptions-item label="E-mail验证标记">
        {{ detailFrom.isEmailVerified }}
      </el-descriptions-item>
      <el-descriptions-item label="匿名用户">
        {{ detailFrom.isAnonymous }}
      </el-descriptions-item>
      <el-descriptions-item label="订阅过期时间">
        {{ detailFrom.subscriptionExpireAt }}
      </el-descriptions-item>
      <el-descriptions-item label="封号">
        {{ detailFrom.lockedAt }}
      </el-descriptions-item>
      <el-descriptions-item label="解锁的剧目数">
        {{ detailFrom.unlockDrama }}
      </el-descriptions-item>
      <el-descriptions-item label="用户类型">
        {{ detailFrom.userType }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ detailFrom.createdAt }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间">
        {{ detailFrom.updatedAt }}
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script setup>
  // 全量引入格式化工具 请按需保留
  import {
    getDictFunc,
    formatDate,
    formatBoolean,
    filterDict,
    filterDataSource,
    returnArrImg,
    onDownloadFile
  } from '@/utils/format'
  import {
    findUsers
  } from '@/api/management/users'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive,onMounted} from 'vue'
  import { useAppStore } from '@/pinia'
  const appStore = useAppStore()

  const props = defineProps({
    id: {
      type: String,
      default: 0
    }
  })
  onMounted(()=>{
    loadUsersFunc()
  })

  // 查看详情控制标记
  const detailShow = ref(false)

  const detailFrom = ref({})

  // 更新行
  const loadUsersFunc = async (row) => {
    const res = await findUsers({ id: props.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }


  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  const emits = defineEmits(['close'])

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
    emits('close')
  }

</script>
