<template>
  <div>
    <div class="gva-search-box">
      <el-form
        ref="elSearchFormRef"
        :inline="true"
        :model="searchInfo"
        class="demo-form-inline"
        :rules="searchRule"
        @keyup.enter="onSubmit"
      >
        <el-form-item label="订单ID">
          <el-input v-model="searchInfo.id" placeholder="订单ID" clearable style="width: 200px"/>
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.userId" placeholder="用户ID" clearable style="width: 200px"/>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchInfo.orderStatus"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in orderStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="支付订单ID">
          <el-input
            v-model="searchInfo.paymentOrderId"
            placeholder="支付订单ID"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="Email">
          <el-input
            v-model="searchInfo.email"
            placeholder="email"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="Name">
          <el-input
            v-model="searchInfo.username"
            placeholder="name"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="剧ID">
          <el-input
            v-model="searchInfo.videoId"
            placeholder="剧ID"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit"
            >查询</el-button
          >
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column align="left" label="订单ID" prop="id" width="160" />
        <el-table-column
          align="left"
          label="用户ID"
          prop="userId"
          width="160"
        >
          <template #default="scope">
            <el-tag @click="handleQueryUser(scope.row.userId)" style="text-decoration: underline;cursor: pointer;">
              {{ scope.row.userId }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="订阅ID"
          prop="planId"
          width="160"
        />
        <el-table-column
          align="left"
          label="支付账户ID"
          prop="accountId"
          width="160"
        />
        <el-table-column
          align="left"
          label="支付订单ID"
          prop="paymentOrderId"
          width="270"
        >
          <template #default="scope">
            <div style="display: flex;flex-wrap: wrap;word-break: break-all;">
              {{ scope.row.paymentOrderId }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="订单状态"
          prop="orderStatus"
          width="120"
        >
          <template #default="scope">{{
            filterOrderStatus(scope.row.orderStatus)
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="首次支付金额"
          prop="firstAmount"
          width="120"
        >
          <template #default="scope">{{
            scope.row.firstAmount ? scope.row.firstAmount / 100 : '--'
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="续费金额"
          prop="amount"
          width="120"
        >
          <template #default="scope">{{
            scope.row.amount ? scope.row.amount / 100 : '--'
          }}</template>
        </el-table-column>
        <el-table-column
          align="left"
          label="订阅周期"
          prop="period"
          width="120"
        />
        <el-table-column
          align="left"
          label="开始时间"
          prop="startTime"
          width="180"
        >
          <template #default="scope">{{
            formatDate(scope.row.startTime)
          }}</template>
        </el-table-column>
        <el-table-column
          align="center"
          label="操作"
          fixed="right"
          width="180"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              class="table-button"
              @click="getDetails(scope.row)"
              ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
              >查看</el-button
            >
            <el-button
              v-if="scope.row.orderStatus == 'subed' || scope.row.orderStatus == 'paid' || scope.row.orderStatus == 'cancel'"
              type="primary"
              link
              class="table-button"
              @click="getTransaction(scope.row)"
              >流水</el-button
            >
            <el-button
              v-if="scope.row.orderStatus == 'subed' || scope.row.orderStatus == 'paid'"
              type="danger"
              link
              class="table-button"
              @click="cancelRow(scope.row)">
              退订</el-button
            >
            <el-button
              v-if="scope.row.orderStatus == 'subed' || scope.row.orderStatus == 'paid' || scope.row.orderStatus == 'cancel'"
              type="danger"
              link
              class="table-button"
              @click="refundRow(scope.row)">
              退款</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="80px"
      >
        <el-form-item label="订单ID:" prop="id">
          <el-input
            v-model.number="formData.id"
            :clearable="true"
            placeholder="请输入订单ID"
          />
        </el-form-item>
        <el-form-item label="用户ID:" prop="userId">
          <el-input
            v-model.number="formData.userId"
            :clearable="true"
            placeholder="请输入用户ID"
          />
        </el-form-item>
        <el-form-item label="订阅ID:" prop="planId">
          <el-input
            v-model.number="formData.planId"
            :clearable="true"
            placeholder="请输入订阅ID"
          />
        </el-form-item>
        <el-form-item label="支付账户ID:" prop="accountId">
          <el-input
            v-model.number="formData.accountId"
            :clearable="true"
            placeholder="请输入支付账户ID"
          />
        </el-form-item>
        <el-form-item label="支付订单ID:" prop="paymentOrderId">
          <el-input
            v-model="formData.paymentOrderId"
            :clearable="true"
            placeholder="请输入支付订单ID"
          />
        </el-form-item>
        <el-form-item label="首次支付金额:" prop="firstAmount">
          <el-input
            v-model.number="formData.firstAmount"
            :clearable="true"
            placeholder="请输入首次支付金额"
          />
        </el-form-item>
        <el-form-item label="续费金额:" prop="amount">
          <el-input
            v-model.number="formData.amount"
            :clearable="true"
            placeholder="请输入续费金额"
          />
        </el-form-item>
        <el-form-item label="订阅周期:" prop="period">
          <el-input
            v-model.number="formData.period"
            :clearable="true"
            placeholder="请输入订阅周期"
          />
        </el-form-item>
        <el-form-item label="开始时间:" prop="startTime">
          <el-date-picker
            v-model="formData.startTime"
            type="date"
            style="width: 100%"
            placeholder="选择日期"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item label="结束时间:" prop="periodEndTime">
          <el-date-picker
            v-model="formData.periodEndTime"
            type="date"
            style="width: 100%"
            placeholder="选择日期"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item label="订阅次数:" prop="subTimes">
          <el-input
            v-model.number="formData.subTimes"
            :clearable="true"
            placeholder="请输入订阅次数"
          />
        </el-form-item>
        <el-form-item label="取消时间:" prop="cancelTime">
          <el-date-picker
            v-model="formData.cancelTime"
            type="date"
            style="width: 100%"
            placeholder="选择日期"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item label="订单状态:" prop="orderStatus">
          <el-input
            v-model="formData.orderStatus"
            :clearable="true"
            placeholder="请输入订单状态"
          />
        </el-form-item>
        <el-form-item label="附加信息:" prop="attach">
          <el-input
            v-model="formData.attach"
            :clearable="true"
            placeholder="请输入附加信息"
          />
        </el-form-item>
        <el-form-item label="套餐类型:" prop="planType">
          <el-input
            v-model="formData.planType"
            :clearable="true"
            placeholder="请输入套餐类型"
          />
        </el-form-item>
        <el-form-item label="解锁的剧目数:" prop="dramaCount">
          <el-input
            v-model.number="formData.dramaCount"
            :clearable="true"
            placeholder="请输入解锁的剧目数"
          />
        </el-form-item>
        <el-form-item label="创建时间:" prop="createdAt">
          <el-date-picker
            v-model="formData.createdAt"
            type="date"
            style="width: 100%"
            placeholder="选择日期"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item label="更新时间:" prop="updatedAt">
          <el-date-picker
            v-model="formData.updatedAt"
            type="date"
            style="width: 100%"
            placeholder="选择日期"
            :clearable="true"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="detailShow"
      :show-close="true"
      :before-close="closeDetailShow"
      title="查看"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="订单ID">
          {{ detailFrom.id }}
        </el-descriptions-item>
        <el-descriptions-item label="用户ID">
          {{ detailFrom.userId }}
        </el-descriptions-item>
        <el-descriptions-item label="订阅ID">
          {{ detailFrom.planId }}
        </el-descriptions-item>
        <el-descriptions-item label="支付账户ID">
          {{ detailFrom.accountId }}
        </el-descriptions-item>
        <el-descriptions-item label="支付订单ID">
          {{ detailFrom.paymentOrderId }}
        </el-descriptions-item>
        <el-descriptions-item label="首次支付金额">
          {{ detailFrom.firstAmount ? detailFrom.firstAmount / 100 : '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="续费金额">
          {{ detailFrom.amount ? detailFrom.amount / 100 : '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="订阅周期">
          {{ detailFrom.period }}
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">
          {{ formatDate(detailFrom.startTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="结束时间">
          {{ formatDate(detailFrom.periodEndTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="订阅次数">
          {{ detailFrom.subTimes }}
        </el-descriptions-item>
        <el-descriptions-item label="取消时间">
          {{ detailFrom.cancelTime }}
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          {{ detailFrom.orderStatus }}
        </el-descriptions-item>
        <el-descriptions-item label="附加信息" min-width="120">
          <div style="display: flex;flex-wrap: wrap;word-break: break-all;">
          {{ detailFrom.attach }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="套餐类型">
          {{ detailFrom.planType }}
        </el-descriptions-item>
        <el-descriptions-item label="解锁的剧目数">
          {{ detailFrom.dramaCount }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(detailFrom.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDate(detailFrom.updatedAt) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>

    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="transShow"
      :show-close="true"
      :before-close="closeTransShow"
      title="支付流水"
    >
      <el-table
        :data="transForm"
        style="width: 100%"
      >
        <el-table-column
          align="left"
          label="ID"
          prop="id" />
        <el-table-column
          align="left"
          label="支付ID"
          prop="transaction_id" />
        <el-table-column
          align="left"
          label="金额">
          <template #default="scope">
            {{ scope.row.amount / 100 }}
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="时间"
          prop="created_at" />
      </el-table>
    </el-drawer>

    <div v-if="userInfo.show">
      <query-user :id="userInfo.id" @close="closeUserDetail"/>
    </div>
  </div>
</template>

<script setup>
  import {
    createPaymentOrder,
    deletePaymentOrder,
    deletePaymentOrderByIds,
    updatePaymentOrder,
    findPaymentOrder,
    getPaymentOrderList,
    cancelPaymentOrder,
    refundPaymentOrder,
    getPaymentTransaction,
  } from '@/api/management/paymentOrder'

  // 全量引入格式化工具 请按需保留
  import {
    formatDate,
  } from '@/utils/format'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive } from 'vue'
  import { useAppStore } from '@/pinia'
  import queryUser from './queryUser.vue'
  defineOptions({
    name: 'PaymentOrder'
  })

  let orderStatusOptions = ref([
    {
      label: '初始化',
      value: 'init'
    },
    {
      label: '待支付',
      value: 'pending'
    },
    {
      label: '首次支付完成',
      value: 'paid'
    },
    {
      label: '订阅中',
      value: 'subed'
    },
    {
      label: '取消订阅',
      value: 'cancel'
    },
    {
      label: '支付失败',
      value: 'failed'
    },
    {
      label: '退款',
      value: 'refund'
    }
  ])

  const filterOrderStatus = (item)=>{
    if(item){
      return orderStatusOptions.value.filter((val)=>val.value == item)[0]?.label
    }else{
      return '--'
    }
  }

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  // 控制更多查询条件显示/隐藏状态
  const showAllQuery = ref(false)

  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
    id: undefined,
    userId: undefined,
    planId: undefined,
    accountId: undefined,
    paymentOrderId: '',
    firstAmount: undefined,
    amount: undefined,
    period: undefined,
    startTime: new Date(),
    periodEndTime: new Date(),
    subTimes: undefined,
    cancelTime: new Date(),
    orderStatus: '',
    attach: '',
    planType: '',
    dramaCount: undefined,
    createdAt: new Date(),
    updatedAt: new Date()
  })

  // 验证规则
  const rule = reactive({})

  const searchRule = reactive({
    createdAt: [
      {
        validator: (rule, value, callback) => {
          if (
            searchInfo.value.startCreatedAt &&
            !searchInfo.value.endCreatedAt
          ) {
            callback(new Error('请填写结束日期'))
          } else if (
            !searchInfo.value.startCreatedAt &&
            searchInfo.value.endCreatedAt
          ) {
            callback(new Error('请填写开始日期'))
          } else if (
            searchInfo.value.startCreatedAt &&
            searchInfo.value.endCreatedAt &&
            (searchInfo.value.startCreatedAt.getTime() ===
              searchInfo.value.endCreatedAt.getTime() ||
              searchInfo.value.startCreatedAt.getTime() >
                searchInfo.value.endCreatedAt.getTime())
          ) {
            callback(new Error('开始日期应当早于结束日期'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  })

  const elFormRef = ref()
  const elSearchFormRef = ref()

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(10)
  const tableData = ref([])
  const searchInfo = ref({})
  // 重置
  const onReset = () => {
    searchInfo.value = {}
    getTableData()
  }

  // 搜索
  const onSubmit = () => {
    elSearchFormRef.value?.validate(async (valid) => {
      if (!valid) return
      page.value = 1
      getTableData()
    })
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查询
  const getTableData = async () => {
    const table = await getPaymentOrderList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  getTableData()

  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 多选数据
  const multipleSelection = ref([])
  // 多选
  const handleSelectionChange = (val) => {
    multipleSelection.value = val
  }
  const transForm = ref([])
  const transShow = ref(false)

  // 打开流水弹窗
  const openTransShow = () => {
    transShow.value = true
  }
  // 打开流水
  const getTransaction = async (row) => {
    // 打开弹窗
    const res = await getPaymentTransaction({ id: row.id })
    if (res.code === 0) {
      transForm.value = res.data
      openTransShow()
    }
  }
  // 关闭流水弹窗
  const closeTransShow = () => {
    transShow.value = false
    transForm.value = []
  }

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deletePaymentOrderFunc(row)
    })
  }

  //
  const cancelRow = (row) => {
    ElMessageBox.confirm('确定要退订吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      cancelPaymentOrderFunc(row)
    })
  }

  //
  const refundRow = (row) => {
    ElMessageBox.confirm('确定要退款吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      refundPaymentOrderFunc(row)
    })
  }


  // 多选删除
  const onDelete = async () => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map((item) => {
          ids.push(item.id)
        })
      const res = await deletePaymentOrderByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
    })
  }

  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 更新行
  const updatePaymentOrderFunc = async (row) => {
    const res = await findPaymentOrder({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
      formData.value = res.data
      dialogFormVisible.value = true
    }
  }

  // 删除行
  const deletePaymentOrderFunc = async (row) => {
    const res = await deletePaymentOrder({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }


  // 退订
  const cancelPaymentOrderFunc = async (row) => {
    const res = await cancelPaymentOrder({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '退订成功'
      })
      getTableData()
    }
  }
  // 退款
  const refundPaymentOrderFunc = async (row) => {
    const res = await refundPaymentOrder({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '退款成功'
      })
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      userId: undefined,
      planId: undefined,
      accountId: undefined,
      paymentOrderId: '',
      firstAmount: undefined,
      amount: undefined,
      period: undefined,
      startTime: new Date(),
      periodEndTime: new Date(),
      subTimes: undefined,
      cancelTime: new Date(),
      orderStatus: '',
      attach: '',
      planType: '',
      dramaCount: undefined,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  // 弹窗确定
  const enterDialog = async () => {
    btnLoading.value = true
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createPaymentOrder(formData.value)
          break
        case 'update':
          res = await updatePaymentOrder(formData.value)
          break
        default:
          res = await createPaymentOrder(formData.value)
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建/更改成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }

  const detailFrom = ref({})

  // 查看详情控制标记
  const detailShow = ref(false)

  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findPaymentOrder({ id: row.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }

  let userInfo = ref({
    show:false,
    id:""
  })
  const handleQueryUser = (id)=>{
    userInfo.value.show = true
    userInfo.value.id = id
  }
  const closeUserDetail = ()=>{
    userInfo.value.show = !userInfo.value.show
  }
</script>

<style></style>
