<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog()"
          >新增</el-button
        >
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
      >
        <el-table-column align="left" label="主键" prop="id" />
        <el-table-column
          align="left"
          label="Pixel ID"
          prop="configValue"
        />
        <el-table-column
          align="left"
          label="域名"
          prop="website"
        />
        <el-table-column
          align="left"
          label="平台"
          prop="configKey"
        />
        <el-table-column
          align="right"
          label="操作"
          fixed="right"
          width="180"
          :min-width="appStore.operateMinWith"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="delete"
              @click="deleteRow(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="80px"
      >
        <el-form-item label="Pixel ID:" prop="configValue">
          <el-input
            v-model="formData.configValue"
            :clearable="true"
            placeholder="请输入Pixel ID"
          />
        </el-form-item>
        <el-form-item label="域名:" prop="website">
          <el-input
            v-model="formData.website"
            :clearable="true"
            placeholder="请输入域名"
          />
        </el-form-item>
        <el-form-item label="平台:" prop="configKey">
          <el-select
            v-model="formData.configKey"
            placeholder="平台"
            size="large"
            style="width: 240px"
          >
            <el-option
              v-for="item in platformOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :default-first-option="true"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-drawer>

  </div>
</template>

<script setup>
  import {
    createPixel,
    deletePixel,
    getPixelList,
  } from '@/api/management/pixel'

  // 全量引入格式化工具 请按需保留
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive } from 'vue'
  import { useAppStore } from '@/pinia'

  let platformOptions = ref([
    {
      label: 'Facebook',
      value: 'facebook_pixel_id'
    },
    {
      label: 'Tiktok',
      value: 'tiktok_pixel'
    },
    {
      label: 'Google Analytics',
      value: 'google_analytics'
    },
    {
      label: 'Bigo',
      value: 'bigo_pixel'
    },
    {
      label: 'Pinterest',
      value: 'pinterest_pixel'
    }
  ])

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
    id: undefined,
    configValue: '',
    configKey: '',
  })

  // 验证规则
  const rule = reactive({})

  const elFormRef = ref()

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const tableData = ref([])

  // 查询
  const getTableData = async () => {
    const table = await getPixelList()
    if (table.code === 0) {
      tableData.value = table.data.list
    }
  }

  getTableData()

  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteAdminFbConvApiFunc(row)
    })
  }

  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 删除行
  const deleteAdminFbConvApiFunc = async (row) => {
    const res = await deletePixel({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      configValue: '',
      configKey: '',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  // 弹窗确定
  const enterDialog = async () => {
    btnLoading.value = true
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createPixel(formData.value)
          break
        default:
          res = await createPixel(formData.value)
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }
</script>

<style></style>
