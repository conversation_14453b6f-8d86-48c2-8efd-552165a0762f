<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog()"
          >新增</el-button
        >
      </div>
      <el-table
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
      >
        <el-table-column align="left" label="ID" prop="id" width="160" />
        <el-table-column
          align="left"
          width="90"
          label="管理员ID"
          prop="adminId"
        />
        <el-table-column align="left" label="网站" prop="website" width="270" />
        <el-table-column align="left" label="组织" prop="organization.name" width="200" />
        <el-table-column
          align="left"
          label="创建时间"
          prop="createdAt"
          width="180"
        >
          <template #default="scope">{{
            formatDate(scope.row.createdAt)
          }}</template>
        </el-table-column>

        <el-table-column
          align="left"
          label="检查项"
          width="800"
        >
          <template #default="scope">
            <el-button-group class="check-button-group">
              <el-button
                :type="checkResults[scope.row.id]?.parse?.success ? 'success' : 'primary'"
                :loading="checkLoading.parse"
                link
                @click="checkParse(scope.row)"
              >
                {{ checkResults[scope.row.id]?.parse ? checkResults[scope.row.id].parse.message : '检查解析' }}
              </el-button>
              <el-button
                :type="checkResults[scope.row.id]?.payment?.success ? 'success' : 'primary'"
                :loading="checkLoading.payment"
                link
                @click="checkPayment(scope.row)"
              >
                {{ checkResults[scope.row.id]?.payment ? checkResults[scope.row.id].payment.message : '检查支付' }}
              </el-button>
              <el-button
                :type="checkResults[scope.row.id]?.cdn?.success ? 'success' : 'primary'"
                :loading="checkLoading.cdn"
                link
                @click="checkCDN(scope.row)"
              >
                {{ checkResults[scope.row.id]?.cdn ? checkResults[scope.row.id].cdn.message : '检查CDN' }}
              </el-button>
              <el-button
                :type="checkResults[scope.row.id]?.email?.success ? 'success' : 'primary'"
                :loading="checkLoading.email"
                link
                @click="checkEmail(scope.row)"
              >
                {{ checkResults[scope.row.id]?.email ? checkResults[scope.row.id].email.message : '检查邮件' }}
              </el-button>
              <el-button
                :type="checkResults[scope.row.id]?.customerService?.success ? 'success' : 'primary'"
                :loading="checkLoading.customerService"
                link
                @click="checkCustomerService(scope.row)"
              >
                {{ checkResults[scope.row.id]?.customerService ? checkResults[scope.row.id].customerService.message : '检查客服' }}
              </el-button>
              <el-button
                :type="checkResults[scope.row.id]?.proxy?.success ? 'success' : 'primary'"
                :loading="checkLoading.proxy"
                link
                @click="checkProxy(scope.row)"
              >
                {{ checkResults[scope.row.id]?.proxy ? checkResults[scope.row.id].proxy.message : '检查代理' }}
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>

        <el-table-column
          align="right"
          label="操作"
          fixed="right"
          width="180"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              class="table-button"
              @click="getDetails(scope.row)"
              ><el-icon style="margin-right: 5px"><InfoFilled /></el-icon
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="delete"
              @click="deleteRow(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '编辑' }}</span>
          <div>
            <el-button :loading="btnLoading" type="primary" @click="enterDialog"
              >确 定</el-button
            >
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form
        :model="formData"
        label-position="top"
        ref="elFormRef"
        :rules="rule"
        label-width="80px"
      >
        <el-form-item label="组织:" prop="organizationId">
          <el-select v-model="formData.organizationId" placeholder="请选择组织" clearable style="width: 100%">
            <el-option
              v-for="item in organizationOptions"
              :key="item.id"
              :label="item.name"
              :value="Number(item.id)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="网站:" prop="website">
          <el-input
            v-model="formData.website"
            :clearable="true"
            placeholder="请输入网站"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <el-drawer
      destroy-on-close
      :size="appStore.drawerSize"
      v-model="detailShow"
      :show-close="true"
      :before-close="closeDetailShow"
      title="查看"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="ID">
          {{ detailFrom.id }}
        </el-descriptions-item>
        <el-descriptions-item label="管理员ID">
          {{ detailFrom.adminId }}
        </el-descriptions-item>
        <el-descriptions-item label="网站">
          {{ detailFrom.website }}
        </el-descriptions-item>
        <el-descriptions-item label="组织">
          {{ detailFrom.organization.name }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detailFrom.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detailFrom.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup>
  import {
    createWebsites,
    deleteWebsites,
    findWebsites,
    getWebsitesList
  } from '@/api/management/website'
  import { getOrganizationList } from '@/api/organizations'

  // 全量引入格式化工具 请按需保留
  import { formatDate } from '@/utils/format'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ref, reactive, onMounted } from 'vue'
  import { useAppStore } from '@/pinia'
  import { checkDNS } from '@/api/checker'

  defineOptions({
    name: 'Websites'
  })

  // 提交按钮loading
  const btnLoading = ref(false)
  const appStore = useAppStore()

  // 组织选项
  const organizationOptions = ref([])

  // 获取组织列表
  const getOrganizationOptions = async () => {
    try {
      const res = await getOrganizationList({ enabled: 1, page: 1, pageSize: 999 })
      if (res.code === 0) {
        organizationOptions.value = res.data.list
      }
    } catch (error) {
      console.error('获取组织列表失败:', error)
    }
  }

  // 自动化生成的字典（可能为空）以及字段
  const formData = ref({
    id: '',
    adminId: undefined,
    website: '',
    organizationId: '',
    createdAt: new Date(),
    updatedAt: new Date()
  })

  // 验证规则
  const rule = reactive({
    website: [
      { required: true, message: '请输入网站', trigger: 'blur' }
    ],
    organizationId: [
      { required: true, message: '请选择组织', trigger: 'change' }
    ]
  })

  const elFormRef = ref()

  // =========== 表格控制部分 ===========
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(100)
  const tableData = ref([])
  const searchInfo = ref({})

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  // 修改页面容量
  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查询
  const getTableData = async () => {
    const table = await getWebsitesList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo.value
    })
    if (table.code === 0) {
      tableData.value = table.data.list
      total.value = table.data.total
      page.value = table.data.page
      pageSize.value = table.data.pageSize
    }
  }

  onMounted(() => {
    getTableData()
    getOrganizationOptions()
  })

  // ============== 表格控制部分结束 ===============

  // 获取需要的字典 可能为空 按需保留
  const setOptions = async () => {}

  // 获取需要的字典 可能为空 按需保留
  setOptions()

  // 删除行
  const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteWebsitesFunc(row)
    })
  }

  // 行为控制标记（弹窗内部需要增还是改）
  const type = ref('')

  // 删除行
  const deleteWebsitesFunc = async (row) => {
    const res = await deleteWebsites({ id: row.id })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === 1 && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  }

  // 弹窗控制标记
  const dialogFormVisible = ref(false)

  // 打开弹窗
  const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
      id: undefined,
      adminId: undefined,
      website: '',
      organizationId: '',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  // 弹窗确定
  const enterDialog = async () => {
    btnLoading.value = true
    elFormRef.value?.validate(async (valid) => {
      if (!valid) return (btnLoading.value = false)
      let res
      switch (type.value) {
        case 'create':
          res = await createWebsites({
            website: formData.value.website,
            organizationId: formData.value.organizationId
          })
          break
        default:
          res = await createWebsites({
            website: formData.value.website,
            organizationId: formData.value.organizationId
          })
          break
      }
      btnLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '创建成功'
        })
        closeDialog()
        getTableData()
      }
    })
  }

  const detailFrom = ref({})

  // 查看详情控制标记
  const detailShow = ref(false)

  // 打开详情弹窗
  const openDetailShow = () => {
    detailShow.value = true
  }

  // 打开详情
  const getDetails = async (row) => {
    // 打开弹窗
    const res = await findWebsites({ id: row.id })
    if (res.code === 0) {
      detailFrom.value = res.data
      openDetailShow()
    }
  }

  // 关闭详情弹窗
  const closeDetailShow = () => {
    detailShow.value = false
    detailFrom.value = {}
  }

  // 检查结果状态
  const checkResults = ref({})

  // 检查按钮状态
  const checkLoading = ref({
    parse: false,
    payment: false,
    cdn: false,
    email: false,
    customerService: false,
    proxy: false
  })

  // 检查解析
  const checkParse = async (row) => {
    checkLoading.value.parse = true
    try {
      const res = await checkDNS({ domain: row.website })
      if (res.code === 0) {
        if (res.data.success) {
          checkResults.value[row.id] = {
            ...checkResults.value[row.id],
            parse: {success: true, message: "解析正常"}
          }
        } else {
          ElMessage.error(res.data.mesg)
        }
      }
    } catch (error) {
      ElMessage.error('检查失败：' + error.message)
    } finally {
      checkLoading.value.parse = false
    }
  }

  // 检查支付
  const checkPayment = async (row) => {
    checkLoading.value.payment = true
    try {
      // TODO: 调用支付检查API
      const result = await new Promise(resolve => setTimeout(() => resolve({ success: true, message: '支付正常' }), 1000))
      checkResults.value[row.id] = {
        ...checkResults.value[row.id],
        payment: result
      }
    } catch (error) {
      ElMessage.error('检查失败：' + error.message)
    } finally {
      checkLoading.value.payment = false
    }
  }

  // 检查CDN
  const checkCDN = async (row) => {
    checkLoading.value.cdn = true
    try {
      // TODO: 调用CDN检查API
      const result = await new Promise(resolve => setTimeout(() => resolve({ success: true, message: 'CDN正常' }), 1000))
      checkResults.value[row.id] = {
        ...checkResults.value[row.id],
        cdn: result
      }
    } catch (error) {
      ElMessage.error('检查失败：' + error.message)
    } finally {
      checkLoading.value.cdn = false
    }
  }

  // 检查邮件
  const checkEmail = async (row) => {
    checkLoading.value.email = true
    try {
      // TODO: 调用邮件检查API
      const result = await new Promise(resolve => setTimeout(() => resolve({ success: true, message: '邮件正常' }), 1000))
      checkResults.value[row.id] = {
        ...checkResults.value[row.id],
        email: result
      }
    } catch (error) {
      ElMessage.error('检查失败：' + error.message)
    } finally {
      checkLoading.value.email = false
    }
  }

  // 检查客服
  const checkCustomerService = async (row) => {
    checkLoading.value.customerService = true
    try {
      // TODO: 调用客服检查API
      const result = await new Promise(resolve => setTimeout(() => resolve({ success: true, message: '客服正常' }), 1000))
      checkResults.value[row.id] = {
        ...checkResults.value[row.id],
        customerService: result
      }
    } catch (error) {
      ElMessage.error('检查失败：' + error.message)
    } finally {
      checkLoading.value.customerService = false
    }
  }

  // 检查代理
  const checkProxy = async (row) => {
    checkLoading.value.proxy = true
    try {
      // TODO: 调用代理检查API
      const result = await new Promise(resolve => setTimeout(() => resolve({ success: true, message: '代理正常' }), 1000))
      checkResults.value[row.id] = {
        ...checkResults.value[row.id],
        proxy: result
      }
    } catch (error) {
      ElMessage.error('检查失败：' + error.message)
    } finally {
      checkLoading.value.proxy = false
    }
  }
</script>

<style>
.check-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 4px;
}

.check-button-group .el-button {
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 4px;
  transition: all 0.3s ease;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}

.check-button-group .el-button:hover {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.check-button-group .el-button:active {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.check-button-group .el-button.is-success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

.check-button-group .el-button.is-success:hover {
  background-color: #e1f3d8;
  border-color: #67c23a;
  color: #67c23a;
}
</style>
