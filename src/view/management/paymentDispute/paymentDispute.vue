
<template>
  <div>
    
    <div class="gva-table-box">
        
        <el-table
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        >
          <el-table-column align="left" label="ID" prop="id" width="240" />
          <el-table-column align="left" label="订单ID" prop="orderId" width="240" />
          <el-table-column align="left" label="争议ID" prop="disputeId" width="240" />
          <el-table-column align="left" label="争议内容" prop="disputeContent" width="240" />
          <el-table-column align="left" label="争议标签" prop="disputeLabel" width="240" />
          <el-table-column align="left" label="处理方式" prop="method" width="240" />
         <el-table-column align="left" label="更新时间" prop="updatedAt" width="180">
            <template #default="scope">{{ formatDate(scope.row.updatedAt) }}</template>
         </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updatePaymentDisputeFunc(scope.row)">编辑</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="争议内容:"  prop="disputeContent" >
              <el-input v-model="formData.disputeContent" :clearable="true"  placeholder="请输入争议内容" />
            </el-form-item>
            <el-form-item label="争议标签:"  prop="disputeLabel" >
              <el-input v-model="formData.disputeLabel" :clearable="true"  placeholder="请输入争议标签" />
            </el-form-item>
            <el-form-item label="处理方式:"  prop="method" >
              <el-input v-model="formData.method" :clearable="true"  placeholder="请输入处理方式" />
            </el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="主键">
                        {{ detailFrom.id }}
                    </el-descriptions-item>
                    <el-descriptions-item label="订单ID">
                        {{ detailFrom.orderId }}
                    </el-descriptions-item>
                    <el-descriptions-item label="争议ID">
                        {{ detailFrom.disputeId }}
                    </el-descriptions-item>
                    <el-descriptions-item label="争议内容">
                        {{ detailFrom.disputeContent }}
                    </el-descriptions-item>
                    <el-descriptions-item label="争议标签">
                        {{ detailFrom.disputeLabel }}
                    </el-descriptions-item>
                    <el-descriptions-item label="处理方式">
                        {{ detailFrom.method }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">
                        {{ detailFrom.createdAt }}
                    </el-descriptions-item>
                    <el-descriptions-item label="更新时间">
                        {{ detailFrom.updatedAt }}
                    </el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  updatePaymentDispute,
  findPaymentDispute,
  getPaymentDisputeList
} from '@/api/management/paymentDispute'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"


defineOptions({
    name: 'PaymentDispute'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            disputeContent: '',
            disputeLabel: '',
            method: '',
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({

})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(30)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getPaymentDisputeList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updatePaymentDisputeFunc = async(row) => {
    const res = await findPaymentDispute({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        id: undefined,
        orderId: undefined,
        disputeId: '',
        disputeContent: '',
        disputeLabel: '',
        method: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createPaymentDispute(formData.value)
                  break
                case 'update':
                  res = await updatePaymentDispute(formData.value)
                  break
                default:
                  res = await createPaymentDispute(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}


const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findPaymentDispute({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>

</style>
