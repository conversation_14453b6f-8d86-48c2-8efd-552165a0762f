<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true">
        <el-form-item label="时间">
          <el-date-picker
            v-model="searchInfo.times"
            style="width: 400px"
            type="datetimerange"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="shortcuts"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="changeTime"
          />
        </el-form-item>
        <div v-if="userStore.userInfo.authorities.filter(
                  (i) => i.authorityId == 888 || i.authorityId == 3000 || i.authorityId == 4000
                ).length > 0">
          <el-form-item label="token">
            <el-select v-model="searchInfo.token" placeholder="选择token" clearable style="width: 300px;">
              <el-option label="全部" value="" />
              <el-option
                v-for="item in refererTokens"
                :key="item.id"
                :label="item.refererToken"
                :value="item.refererToken"
              />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>总业绩</span>
              </div>
            </template>
            <div class="card-value">{{ formattedTotalPerformance }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>总有效数</span>
              </div>
            </template>
            <div class="card-value">{{ totalCnt }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>总重复数</span>
              </div>
            </template>
            <div class="card-value">{{ totalDupCnt }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>重复率</span>
              </div>
            </template>
            <div class="card-value">{{ duplicateRate }}%</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-table
      style="width: 100%"
      tooltip-effect="dark"
      :data="tableData"
      row-key="amount"
      >
        <el-table-column align="left" label="订单金额" prop="amount" sortable />
        <el-table-column align="left" label="有效数" prop="cnt" />
        <el-table-column align="left" label="重复数" prop="dup_cnt" />
        <el-table-column align="left" label="业绩" prop="total">
          <template #default="scope">
            {{ scope.row.total.toFixed(2) }}
          </template>
        </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import { GetPerformance } from '@/api/realtime/index'
  import dayjs from 'dayjs'
  import { useUserStore } from '@/pinia/modules/user'
  import {
    getAdminRefererTokensList
  } from '@/api/management/adminRefererTokens'
  import { todayBegin } from '@/utils/date'
  
  const userStore = useUserStore()
  
  onMounted(() => {
    loadRealtimeQuery()
  })

  const shortcuts = [
    {
      text: '今天',
      value: () => {
        if(searchInfo.value.utc.length){
          const end = new Date()
          const start = new Date()
          start.setHours(0, 0, 0, 0)
          return [start, end]
        }else{
          const end = new Date().Now()
          const start = new Date().Now()
          start.setHours(0, 0, 0, 0)
          return [start, end]
        }
      }
    },
    {
      text: '昨天',
      value: () => {
        if(searchInfo.value.utc.length){
          const end = new Date()
          const start = new Date()
          start.setDate(start.getDate() - 1)
          start.setHours(0, 0, 0, 0)
          return [start, end]
        }else{
          const end = new Date().Now()
          const start = new Date().Now()
          start.setDate(start.getDate() - 1)
          start.setHours(0, 0, 0, 0)
          return [start, end]
        }
      }
    },
    {
      text: '最近3天',
      value: () => {
        if(searchInfo.value.utc.length){
          const end = new Date()
          const start = new Date()
          start.setDate(start.getDate() - 2)
          return [start, end]
        }else{
          const end = new Date().Now()
          const start = new Date().Now()
          start.setDate(start.getDate() - 2)
          return [start, end]
        }
      }
    },
    {
      text: '最近7天',
      value: () => {
        if(searchInfo.value.utc.length){
          const end = new Date()
          const start = new Date()
          start.setDate(start.getDate() - 6)
          return [start, end]
        }else{
          const end = new Date().Now()
          const start = new Date().Now()
          start.setDate(start.getDate() - 6)
          return [start, end]
        }
      }
    },
    {
      text: '最近30天',
      value: () => {
        if(searchInfo.value.utc.length){
          const end = new Date()
          const start = new Date()
          start.setDate(start.getDate() - 29)
          return [start, end]
        }else{
          const end = new Date().Now()
          const start = new Date().Now()
          start.setDate(start.getDate() - 29)
          return [start, end]
        }
      }
    },
    {
      text: '当月',
      value: () => {
        if(searchInfo.value.utc.length){
          const end = new Date()
          const start = new Date()
          start.setDate(1) // 设置为当月第一天
          return [start, end]
        }else{
          const end = new Date().Now()
          const start = new Date().Now()
          start.setDate(1) // 设置为当月第一天
          return [start, end]
        }
      }
    }
  ]

  const dateDayjs = dayjs(todayBegin()).format('YYYY-MM-DD HH:mm:ss')

  const searchInfo = ref({
    utc: [],
    bgn_time: dateDayjs,
    end_time: dayjs(new Date().Now()).format('YYYY-MM-DD HH:mm:ss'),
    times: [dateDayjs, dayjs(new Date().Now()).format('YYYY-MM-DD HH:mm:ss')]
  })

  const loadRefererTokens = async () => {
    const res = await getAdminRefererTokensList({
      page: 1,
      pageSize: 1000
    })
    if (res.code === 0) {
      refererTokens.value = res.data.list
    }
  }
  const refererTokens = ref([])
  loadRefererTokens()

  const checkboxUTCTime = () => {
    if (searchInfo.value.utc.length) {
      Object.assign(searchInfo.value, {
        bgn_time: dayjs(new Date().Beijing()).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
        end_time: dayjs(new Date().Beijing()).format('YYYY-MM-DD HH:mm:ss'),
        times: [
          dayjs(new Date().Beijing()).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs(new Date().Beijing()).format('YYYY-MM-DD HH:mm:ss')
        ]
      })
    } else {
      Object.assign(searchInfo.value, {
        bgn_time: dayjs(new Date().Now()).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
        end_time: dayjs(new Date().Now()).format('YYYY-MM-DD HH:mm:ss'),
        times: [
          dayjs(new Date().Now()).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs(new Date().Now()).format('YYYY-MM-DD HH:mm:ss')
        ]
      })
    }
  }
  const changeTime = (event) => {
    try {
      if (event && event.length === 2) {
        let bgnTime = ''
        let endTime = ''
        if (searchInfo.value.utc.length) {
          bgnTime = dayjs(event[0])
            .add(16, 'hour')
            .format('YYYY-MM-DD HH:mm:ss')
          endTime = dayjs(event[1])
            .add(16, 'hour')
            .format('YYYY-MM-DD HH:mm:ss')
        } else {
          bgnTime = event[0]
          endTime = event[1]
        }
        searchInfo.value.bgn_time = bgnTime
        searchInfo.value.end_time = endTime
        searchInfo.value.times = event
      } else {
        searchInfo.value.bgn_time = ''
        searchInfo.value.end_time = ''
        searchInfo.value.times = []
      }

      loadRealtimeQuery()
    } catch (error) {
      console.log(error)
    }
  }

  const onSubmit = () => {
 
    loadRealtimeQuery()
  }

  const onReset = () => {
    searchInfo.value = {
      token: '',
      utc: [],
      bgn_time: dayjs(new Date().Now())
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(new Date().Now()).format('YYYY-MM-DD HH:mm:ss'),
      times: [
        dayjs(new Date().Now()).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        dayjs(new Date().Now()).format('YYYY-MM-DD HH:mm:ss')
      ]
    }
    loadRealtimeQuery()
  }

  let tableData = ref([])
  let totalPerformance = ref(0)
  let totalCnt = ref(0)
  let totalDupCnt = ref(0)
  const loadRealtimeQuery = async () => {
    try {
      totalPerformance.value = 0
      totalCnt.value = 0
      totalDupCnt.value = 0
      if (searchInfo.value.times.length === 2) {
        searchInfo.value.bgn_time = searchInfo.value.times[0]
        searchInfo.value.end_time = searchInfo.value.times[1]
      }
      const res = await GetPerformance({ ...searchInfo.value })
      if (res.code === 0) {
        tableData.value = res?.data ? res.data : []
        for (let i = 0; i < tableData.value.length; i++) {
          totalPerformance.value += tableData.value[i].total
          totalCnt.value += tableData.value[i].cnt
          totalDupCnt.value += tableData.value[i].dup_cnt
        }
      }
    } catch (error) {
      ElMessage.error('查询失败，请稍后重试')
    }
  }

  const duplicateRate = computed(() => {
    const total = totalDupCnt.value + totalCnt.value
    if (total === 0) return '0.00'
    return ((totalDupCnt.value / total) * 100).toFixed(2)
  })

  const formattedTotalPerformance = computed(() => {
    return totalPerformance.value.toFixed(2)
  })
</script>

<style lang="scss" scoped>
  .padding-l-10 {
    padding-left: 10px;
  }
  .el-row {
    margin-bottom: 24px;
  }
  .gva-search-box {
    margin-bottom: 20px;
  }
  .overview-section {
    margin-bottom: 20px;
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
    }
    .card-value {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
      text-align: center;
      padding: 10px 0;
    }
  }
</style>
