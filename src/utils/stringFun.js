/* eslint-disable */
export const toUpperCase = (str) => {
  if (str[0]) {
    return str.replace(str[0], str[0].toUpperCase())
  } else {
    return ''
  }
}

export const toLowerCase = (str) => {
  if (str[0]) {
    return str.replace(str[0], str[0].toLowerCase())
  } else {
    return ''
  }
}

// 驼峰转换下划线
export const toSQLLine = (str) => {
  if (str === 'ID') return 'ID'
  return str.replace(/([A-Z])/g, '_$1').toLowerCase()
}

// 下划线转换驼峰
export const toHump = (name) => {
  return name.replace(/\_(\w)/g, function (all, letter) {
    return letter.toUpperCase()
  })
}

export const OrderStatusToText = (status) => {
  switch (status) {
    case 'init':
      return '已创建'
    case 'pending':
      return '待支付'
    case 'paid':
      return '已支付'
    case 'subed':
      return '订阅中'
    case 'cancel':
      return '已取消'
    case 'failed':
      return '支付失败'
    case 'refund':
      return '已退款'
    default:
      return status
  }
}

export const PaymentTypeToText = (type) => {
  switch (type) {
    case 'paypal':
      return 'PayPal订阅';
    case 'vault_paypal':
      return 'PayPal'
    case 'airwallex':
      return '云汇订阅'
    case 'manual_airwallex':
      return '云汇'
    case 'stripe':
      return 'Stripe订阅'
    case 'manual_stripe':
      return 'Stripe'
    default:
      return type
  }
}

export const PickAndJoinJson = (attach, separator, ...keys) => {
  const obj = JSON.parse(attach)
  if (!obj) return ""
  const arr = []
  keys.forEach((key) => {
    if (obj[key]) {
      arr.push(obj[key])
    }
  })
  return arr.join(separator)
}
