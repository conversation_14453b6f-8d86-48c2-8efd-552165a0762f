name: stg-deploy

on:
  push:
    branches:
      - main

env:
  LARK_URL: ${{ vars.DEPLOY_NOTIFY_URL }}
  IMAGE_ID: x-short-admin-front

jobs:
  deploy:
    runs-on: stg
    steps:
      - name: Checkout
        run: |
          if [ -d ${IMAGE_ID} ]; then rm -rf ${IMAGE_ID}; fi
          git clone --branch=main https://$GITHUB_ACTOR_ID:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY.git ${IMAGE_ID}
      - name: Deploy
        run: |
          cd ${IMAGE_ID}/deploy/stg-admin-front
          COMMIT_URL=$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/commit/$(git rev-parse --short HEAD)
          ACTION_URL=$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID
          sudo docker compose up -d --build && \
          curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[${GITHUB_REF_NAME}][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: Develop server deployed successfully! \nTime: $(date +'%Y-%m-%d %H:%M:%S') \nCommit: $COMMIT_URL\"}}" $LARK_URL || \
          curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[${GITHUB_REF_NAME}][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: Develop server deployed failed! \nTime: $(date +'%Y-%m-%d %H:%M:%S') \nCommit: $COMMIT_URL\nAction: $ACTION_URL\"}}" $LARK_URL
      - name: Clean
        run: |
          if [ $(df -h | grep /dev/root | awk '{print $4}' | sed 's/G//') -lt 10 ]; then
            sudo docker system prune -f && \
            curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[${GITHUB_REF_NAME}][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: Disk space is not enough! Cleaned successfully! \nTime: $(date +'%Y-%m-%d %H:%M:%S')\"}}" $LARK_URL
          else
            echo "Disk space is enough! Skip clean!"
          fi
