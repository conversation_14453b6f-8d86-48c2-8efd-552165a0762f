name: prod-deploy

on:
  workflow_dispatch:
    inputs: {}

env:
  LARK_URL: ${{ vars.DEPLOY_NOTIFY_URL }}
  IMAGE_ID: x-short-admin-front
  IMAGE_TAG: ${{ github.run_id }}
  ECR_HOST: ${{ vars.ECR_HOST }}
  IMAGE: ${{ vars.ECR_HOST }}/x-short-admin-front:${{ github.run_id }}

jobs:
  build:
    runs-on: stg
    steps:
      - name: Checkout
        run: |
          if [ -d ${IMAGE_ID} ]; then rm -rf ${IMAGE_ID}; fi
          git clone --branch=main https://$GITHUB_ACTOR_ID:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY.git ${IMAGE_ID}
      - name: Build
        run: |
          cd ${IMAGE_ID}
          aws ecr get-login-password --region us-west-1 | sudo docker login --username AWS --password-stdin ${ECR_HOST}
          aws ecr describe-repositories --region us-west-1 --repository-name $IMAGE_ID || aws ecr create-repository --region us-west-1 --repository-name  $IMAGE_ID
          sudo docker build -t $IMAGE_ID:$IMAGE_TAG .
          sudo docker tag $IMAGE_ID:$IMAGE_TAG ${ECR_HOST}/$IMAGE_ID:$IMAGE_TAG
          sudo docker push ${ECR_HOST}/$IMAGE_ID:$IMAGE_TAG
          echo "Build image $IMAGE_ID:$IMAGE_TAG successfully!"
  deploy:
    runs-on: prod
    needs: build
    steps:
      - name: Checkout
        run: |
          if [ -d ${IMAGE_ID} ]; then rm -rf ${IMAGE_ID}; fi
          git clone --branch=main https://$GITHUB_ACTOR_ID:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY.git ${IMAGE_ID}
      - name: Deploy
        run: |
          cd ${IMAGE_ID}/deploy/prod-admin-front
          COMMIT_URL=$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/commit/$(git rev-parse --short HEAD)
          ACTION_URL=$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID
          sed -i "s|\${IMAGE}|$IMAGE|g" docker-compose.yml
          aws ecr get-login-password --region us-west-1 | sudo docker login --username AWS --password-stdin ${ECR_HOST}
          sudo docker compose up -d && \
          curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[PROD][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: Admin frontend service deployed successfully! \nTime: $(date +'%Y-%m-%d %H:%M:%S') \nCommit: $COMMIT_URL\"}}" $LARK_URL || \
          curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[PROD][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: Admin frontend service deployed failed! \nTime: $(date +'%Y-%m-%d %H:%M:%S') \nCommit: $COMMIT_URL\nAction: $ACTION_URL\"}}" $LARK_URL
      - name: Clean
        run: |
          if [ $(df -h | grep /dev/root | awk '{print $4}' | sed 's/G//') -lt 10 ]; then
            sudo docker system prune -f && \
            curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[PROD][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: Disk space is not enough! Cleaned successfully! \nTime: $(date +'%Y-%m-%d %H:%M:%S')\"}}" $LARK_URL
          else
            echo "Disk space is enough! Skip clean!"
          fi
