import { viteLogo } from './src/core/config'
import * as path from 'path'
import * as dotenv from 'dotenv'
import * as fs from 'fs'
import vuePlugin from '@vitejs/plugin-vue'
import VueFilePathPlugin from './vitePlugin/componentName/index.js'
import { AddSecret } from './vitePlugin/secret'

// @see https://cn.vitejs.dev/config/
export default ({ mode }) => {
  AddSecret('')
  const NODE_ENV = mode || 'development'
  const envFiles = [`.env.${NODE_ENV}`]
  for (const file of envFiles) {
    const envConfig = dotenv.parse(fs.readFileSync(file))
    for (const k in envConfig) {
      process.env[k] = envConfig[k]
    }
  }

  viteLogo(process.env)

  const timestamp = Date.parse(new Date())

  const optimizeDeps = {
  }

  const alias = {
    '@': path.resolve(__dirname, './src'),
    vue$: 'vue/dist/vue.runtime.esm-bundler.js'
  }

  const esbuild = {}

  const rollupOptions = {
    external: [
      // '@form-create/designer',
      // '@element-plus/icons-vue',
      // 'ace-builds',
      // '@wangeditor/editor',
      // '@wangeditor/editor-for-vue',
      // 'highlight.js',
    ],
    output: {
      entryFileNames: 'assets/[name].[hash].js',
      chunkFileNames: 'assets/[name].[hash].js',
      assetFileNames: 'assets/[name].[hash].[ext]'
    }
  }

  const config = {
    base: '/', // 编译后js导入的资源路径
    root: './', // index.html文件所在位置
    publicDir: 'public', // 静态资源文件夹
    resolve: {
      alias
    },
    define: {
      'process.env': {}
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler' // or "modern"
        }
      }
    },
    server: {
      // 如果使用docker-compose开发模式，设置为false
      open: false,
      port: process.env.VITE_CLI_PORT,
      proxy: {
        // 把key的路径代理到target位置
        // detail: https://cli.vuejs.org/config/#devserver-proxy
        [process.env.VITE_BASE_API]: {
          // 需要代理的路径   例如 '/api'
          target: `${process.env.VITE_BASE_PATH}`, // 代理到 目标路径
          changeOrigin: true,
          secure:false,
          rewrite: (path) =>
            // path.replace(new RegExp('^' + process.env.VITE_BASE_API), '/api')
            path.replace(new RegExp('^' + process.env.VITE_BASE_API), '/')
        }
      }
    },
    build: {
      minify: 'terser', // 是否进行压缩,boolean | 'terser' | 'esbuild',默认使用terser
      manifest: false, // 是否产出manifest.json
      sourcemap: false, // 是否产出sourcemap.json
      outDir: 'dist', // 产出目录
      terserOptions: {
        compress: {
          //生产环境时移除console
          drop_console: true,
          drop_debugger: true
        }
      },
      rollupOptions
    },
    esbuild,
    optimizeDeps,
    plugins: [
      // importToCDN({
      //   modules: [
          // {
          //   name: '@form-create/designer',
          //   var: 'FormCreateDesigner',
          //   path: 'https://cdn.jsdelivr.net/npm/@form-create/designer@3.2.11/dist/index.es.js'
          // },{
          //   name: '@element-plus/icons-vue',
          //   var: 'ElementPlusIconsVue',
          //   path: 'https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.js',
          // },{
          //   name: 'ace-builds',
          //   var: 'ace',
          //   path: 'https://cdnjs.cloudflare.com/ajax/libs/ace/1.39.0/ace.js',
          // },{
          //   name: '@wangeditor/editor',
          //   var: 'wangEditor',
          //   path: 'https://cdn.jsdelivr.net/npm/@wangeditor/editor@5.1.23/dist/index.min.js',
          // },
          // {
          //   name: '@wangeditor/editor-for-vue',
          //   var: 'WangEditorVue',
          //   path: 'https://cdn.jsdelivr.net/npm/@wangeditor/editor-for-vue@5.1.12/dist/index.min.js',
          // },
          // {
          //   name: 'highlight.js',
          //   var: 'highlight.js',
          //   path: 'https://cdn.jsdelivr.net/npm/highlight.js@11.11.1/+esm',
          // },
        // ]
      // }),
      vuePlugin(),
      VueFilePathPlugin('./src/pathInfo.json'),
    ]
  }
  return config
}
