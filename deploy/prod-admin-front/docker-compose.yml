version: "3"
services:
  admin-front-ui:
    image: ${IMAGE}
    container_name: admin-front-ui
    restart: always
    # command: ["sh", "-c", "/main webserver"]
    ports:
      - "18081:80"
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost/ping"]
    #   interval: 10s
    #   retries: 5
    #   start_period: 30s
    #   timeout: 10s
    # volumes:
    #   - /home/<USER>/logs/admin-backend.yaml:/go/src/server/config.docker.yaml:ro
