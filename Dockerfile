FROM node:20-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
WORKDIR /app
COPY package.json .
COPY pnpm-lock.yaml .
# RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm i
RUN pnpm i
COPY . /app
RUN pnpm run build

FROM nginx:alpine
COPY --from=base /app/.docker-compose/nginx/conf.d/my.conf /etc/nginx/conf.d/my.conf
COPY --from=base /app/dist /usr/share/nginx/html
RUN ls -al /usr/share/nginx/html
